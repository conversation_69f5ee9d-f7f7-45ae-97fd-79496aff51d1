"""
Logging configuration utilities for JSMon-NG.

This module provides a centralized logging configuration with support for:
- Colored console output with emojis
- Rich console output with tracebacks (if rich package is installed)
- File logging with rotation
- Warning capture
- Environment variable overrides
- Configuration via LoggingConfig model

Environment variables:
- LOG_LEVEL: Override log level (DEBUG, INFO, etc)
- LOG_FILE: Path to log file
- LOG_USE_RICH: Use RichHandler if set to '1' or 'true' (requires rich package)
"""

import logging
import logging.config
import logging.handlers
import os
import warnings
from pathlib import Path
from typing import Literal, Optional, Union, Dict, Any

from ..config.config import LoggingConfig

# --- Rich Detection ---
try:
    from rich.logging import RichHandler
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

# --- Colorama Setup ---
try:
    import colorama
    colorama.init(autoreset=True)
    from colorama import Fore, Style
    COLOR_ENABLED = True
except ImportError:
    COLOR_ENABLED = False
    class _Dummy:
        def __getattr__(self, name: str) -> str: return ""
    Fore = Style = _Dummy()

# --- Level Map and Emojis ---
LEVEL_MAP = {
    logging.DEBUG:    (Fore.CYAN,    "⚙️ DEBUG"),
    logging.INFO:     (Fore.GREEN,   "ℹ️ INFO"),
    logging.WARNING:  (Fore.YELLOW,  "⚠️ WARNING"),
    logging.ERROR:    (Fore.RED,     "❌ ERROR"),
    logging.CRITICAL: (Fore.MAGENTA + Style.BRIGHT, "🔥 CRITICAL"),
}

# --- Formatter ---
class ColoredFormatter(logging.Formatter):
    """Custom formatter that adds colors and emojis to log messages for standard StreamHandler."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Cache color settings for performance
        self._color_enabled = COLOR_ENABLED
        self._reset_all = Style.RESET_ALL if COLOR_ENABLED else ""

    def format(self, record: logging.LogRecord) -> str:
        """Format a log record with colors and emojis."""
        color, level_prefix = LEVEL_MAP.get(record.levelno, (Fore.WHITE, record.levelname))

        # Format timestamp
        asctime = self.formatTime(record, self.datefmt)
        message = record.getMessage()

        # Build log entry efficiently
        if self._color_enabled:
            parts = [
                f"{Style.DIM}{asctime}{self._reset_all}",
                f"[{record.name}]",
                f"{color}{Style.BRIGHT}{level_prefix:<11}{self._reset_all}",
                message
            ]
            log_entry = " ".join(parts)
        else:
            log_entry = f"{asctime} [{record.name}] {level_prefix:<11} {message}"

        # Handle exceptions
        if record.exc_info and not record.exc_text:
            record.exc_text = self.formatException(record.exc_info)
        if record.exc_text:
            exc_color = f"{Fore.RED}{Style.DIM}" if self._color_enabled else ""
            log_entry = f"{log_entry}\n{exc_color}{record.exc_text}{self._reset_all}"

        return log_entry

class LoggingSettings:
    """Container for logging configuration settings."""

    def __init__(
        self,
        level: str = "INFO",
        log_file: Optional[str] = None,
        use_rich: bool = False,
        backup_count: int = 5,
        max_size: int = 10 * 1024 * 1024,  # 10MB
        log_format: str = "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s",
        date_format: str = "%Y-%m-%d %H:%M:%S"
    ):
        self.level = level
        self.log_file = log_file
        self.use_rich = use_rich
        self.backup_count = backup_count
        self.max_size = max_size
        self.log_format = log_format
        self.date_format = date_format

def _resolve_settings(
    level: Optional[str] = None,
    use_rich: Optional[bool] = None,
    log_settings_model: Optional[LoggingConfig] = None
) -> LoggingSettings:
    """Resolve final logging settings from various sources with proper priority."""
    settings = LoggingSettings()

    # Apply model settings
    if log_settings_model:
        settings.level = log_settings_model.level
        settings.log_file = log_settings_model.file
        settings.backup_count = log_settings_model.backup_count
        settings.max_size = log_settings_model.max_size
        settings.log_format = log_settings_model.format

    # Apply environment variable overrides
    settings.level = os.getenv("LOG_LEVEL", settings.level)
    settings.log_file = os.getenv("LOG_FILE", settings.log_file)

    # Handle rich setting with proper precedence
    if use_rich is not None:
        settings.use_rich = use_rich
    else:
        env_rich = os.getenv("LOG_USE_RICH", "").lower()
        settings.use_rich = env_rich in ("1", "true", "yes")

    # Apply direct function arguments (highest priority)
    if level:
        settings.level = level.upper()

    # Validate rich availability
    if settings.use_rich and not RICH_AVAILABLE:
        warnings.warn(
            "Rich logging was requested but rich package is not installed. "
            "Falling back to standard console handler. "
            "Install rich with: pip install rich"
        )
        settings.use_rich = False

    return settings

def _validate_log_file(log_file: Optional[str]) -> Optional[str]:
    """Validate and prepare log file path."""
    if not log_file:
        return None

    try:
        log_path = Path(log_file)
        # Create parent directories if they don't exist
        log_path.parent.mkdir(parents=True, exist_ok=True)
        return str(log_path)
    except (OSError, ValueError) as e:
        warnings.warn(f"Invalid log file path '{log_file}': {e}. File logging disabled.")
        return None

def _build_handlers_config(settings: LoggingSettings) -> tuple[Dict[str, Any], list[str]]:
    """Build handlers configuration and list."""
    handlers_config: Dict[str, Any] = {}
    root_handlers_list = []

    # Console handler
    if settings.use_rich and RICH_AVAILABLE:
        handlers_config['console_rich'] = {
            'class': 'rich.logging.RichHandler',
            'level': settings.level.upper(),
            'rich_tracebacks': True,
            'show_time': True,
            'show_level': True,
            'show_path': False,
            'markup': True,
        }
        root_handlers_list.append('console_rich')
    else:
        handlers_config['console_standard'] = {
            'class': 'logging.StreamHandler',
            'formatter': 'colored_std',
            'stream': 'ext://sys.stdout',
        }
        root_handlers_list.append('console_standard')

    # File handler
    validated_log_file = _validate_log_file(settings.log_file)
    if validated_log_file:
        handlers_config['file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'file_std',
            'filename': validated_log_file,
            'maxBytes': settings.max_size,
            'backupCount': settings.backup_count,
            'encoding': 'utf-8',
        }
        root_handlers_list.append('file')

    return handlers_config, root_handlers_list

def _build_logging_config(settings: LoggingSettings, handlers_config: Dict[str, Any],
                         root_handlers_list: list[str]) -> Dict[str, Any]:
    """Build the complete logging configuration dictionary."""
    return {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'colored_std': {
                '()': ColoredFormatter,
                'fmt': settings.log_format,
                'datefmt': settings.date_format,
            },
            'file_std': {
                'format': settings.log_format,
                'datefmt': settings.date_format,
            },
        },
        'handlers': handlers_config,
        'root': {
            'handlers': root_handlers_list,
            'level': settings.level.upper(),
        },
        'loggers': {
            'httpx': {'level': 'WARNING', 'propagate': True},
            'httpcore': {'level': 'WARNING', 'propagate': True},
        }
    }

def _apply_logger_overrides(config_dict: Dict[str, Any], log_settings_model: Optional[LoggingConfig]) -> None:
    """Apply logger configuration overrides from the settings model."""
    if not log_settings_model or not hasattr(log_settings_model, 'loggers') or not log_settings_model.loggers:
        return

    for logger_name, logger_config in log_settings_model.loggers.items():
        if logger_name not in config_dict['loggers']:
            config_dict['loggers'][logger_name] = {}
        config_dict['loggers'][logger_name]['level'] = logger_config.get('level', 'WARNING').upper()
        config_dict['loggers'][logger_name]['propagate'] = logger_config.get('propagate', True)

def configure_logging(
    *,
    level: Optional[Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]] = None,
    use_rich: Optional[bool] = None,
    log_settings_model: Optional[LoggingConfig] = None
) -> logging.Logger:
    """Configure logging for the application with proper separation of concerns."""
    # Resolve all settings
    settings = _resolve_settings(level, use_rich, log_settings_model)

    # Build configuration
    handlers_config, root_handlers_list = _build_handlers_config(settings)
    logging_config_dict = _build_logging_config(settings, handlers_config, root_handlers_list)

    # Apply any logger overrides
    _apply_logger_overrides(logging_config_dict, log_settings_model)

    # Configure logging
    logging.config.dictConfig(logging_config_dict)
    logging.captureWarnings(True)

    return logging.getLogger()
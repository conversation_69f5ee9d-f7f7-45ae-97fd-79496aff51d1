"""JSMon-NG Notifications Module.

This module provides notification handlers for various platforms including:
- Discord webhooks
- Telegram bot messages

The module includes:
- Base notification handler with retry logic
- Enhanced formatting utilities
- Payload builders for different platforms
- Centralized notification management
"""

from .discord import Discord<PERSON>and<PERSON>, DiscordConfig
from .telegram import TelegramHandler, TelegramConfig
from .handler import NotificationHandler
from .formatter import EnhancedNotificationFormatter
from .manager import init_handlers, notify_all, shutdown
from .payloads import (
    DiscordEmbed,
    DiscordPayload,
    TelegramPayload,
    NotificationPayload,
    FilePayload,
    build_discord_embed,
    build_discord_payload,
    build_telegram_payload,
    build_notification_payload,
    build_file_payload,
)

__all__ = [
    # Handlers
    "NotificationHandler",
    "DiscordHandler",
    "TelegramHandler",
    # Configs
    "DiscordConfig",
    "TelegramConfig",
    # Formatter
    "EnhancedNotificationFormatter",
    # Manager functions
    "init_handlers",
    "notify_all",
    "shutdown",
    # Payload models
    "DiscordEmbed",
    "DiscordPayload",
    "TelegramPayload",
    "NotificationPayload",
    "FilePayload",
    # Payload builders
    "build_discord_embed",
    "build_discord_payload",
    "build_telegram_payload",
    "build_notification_payload",
    "build_file_payload",
]
"""Prototype pollution detector for JavaScript content analysis.

This module provides functionality to detect prototype pollution vulnerabilities
in JavaScript code, including unsafe object merging, dynamic property access,
and recursive functions that could lead to prototype pollution.
"""

import re
import logging
from typing import Dict, List, Any

# --- Project Modules ---
from jsmon_ng.config.config import AppConfig

log = logging.getLogger(__name__)

# Default configuration values
DEFAULT_CONTEXT_SIZE = 200  # Characters to include before and after matches for analysis
DEFAULT_DISPLAY_CONTEXT_SIZE = 100  # Characters to include before and after matches for display
DEFAULT_RECURSION_LOOKAHEAD = 500  # Characters to look ahead for merge functions in recursive patterns

class PrototypePollutionDetector:
    """Detector for prototype pollution vulnerabilities in JavaScript code."""
    
    def __init__(self, config: AppConfig):
        """Initialize the detector with configuration.
        
        Args:
            config: Application configuration containing vulnerability detection settings
        """
        self.config = config
        self.scanner_config = config.scanner
        
        # Validate configuration
        if not hasattr(self.scanner_config, 'vulnerability_detection'):
            raise ValueError("AppConfig.scanner.vulnerability_detection is required")
        
        self.vuln_config = self.scanner_config.vulnerability_detection
        self.enabled = self.vuln_config.enabled and 'prototype_pollution' in self.vuln_config.detectors
        
        # Patterns for object merging/extending functions that can lead to prototype pollution
        self.merge_patterns = [
            re.compile(r'(?:Object\.assign|Object\.merge|_\.merge|_\.extend|jQuery\.extend|\.extend)\s*\(', re.IGNORECASE),
            re.compile(r'(?:lodash|_)\.(?:merge|extend|defaultsDeep|assignIn|assignInWith|assignWith|defaults|defaultsDeep|merge|mergeWith)\s*\(', re.IGNORECASE),
            re.compile(r'(?:jQuery|\$)\.(?:extend|merge)\s*\(', re.IGNORECASE),
            re.compile(r'(?:deepmerge|merge|extend)\s*\(', re.IGNORECASE)
        ]
        
        # Patterns for dynamic property access that can lead to prototype pollution
        self.dynamic_property_patterns = [
            re.compile(r'obj\[.+?\]', re.IGNORECASE),  # Generic object property access
            re.compile(r'(?:target|dest|result|output|obj|object|config|options)\[.+?\]\s*=', re.IGNORECASE),  # Assignment to dynamic property
            re.compile(r'for\s*\(\s*(?:var|let|const)?\s*(?:\w+|\[\w+,\s*\w+\])\s+(?:in|of)\s+', re.IGNORECASE)  # For-in/for-of loops
        ]
        
        # Patterns for recursive functions that can lead to prototype pollution
        self.recursive_patterns = [
            re.compile(r'function\s+(\w+)\s*\([^)]*\)\s*\{[^{}]*\1\s*\(', re.DOTALL),  # Self-calling function
            re.compile(r'(?:function|const|let|var)\s+(\w+)[^{]*\{[^{}]*\1\s*\(', re.DOTALL)  # Function calling itself
        ]
        
        # Patterns for dangerous property names that can lead to prototype pollution
        self.dangerous_property_patterns = [
            re.compile(r'[\'"]__proto__[\'"]', re.IGNORECASE),
            re.compile(r'[\'"]constructor[\'"]', re.IGNORECASE),
            re.compile(r'[\'"]prototype[\'"]', re.IGNORECASE)
        ]
        
        # Patterns for user input sources
        self.input_source_patterns = [
            re.compile(r'(?:location|document\.URL|window\.name|document\.referrer)', re.IGNORECASE),
            re.compile(r'(?:localStorage|sessionStorage)\.getItem', re.IGNORECASE),
            re.compile(r'JSON\.parse', re.IGNORECASE),
            re.compile(r'(?:req|request)\.(?:body|query|params)', re.IGNORECASE),
            re.compile(r'(?:event|e)\.(?:target|data)', re.IGNORECASE)
        ]
        
        log.info(f"Initialized PrototypePollutionDetector with {len(self.merge_patterns)} merge patterns")
    
    def scan_content(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Scan content for prototype pollution vulnerabilities.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of findings
        """
        if not self.enabled:
            return []
        
        if not content or not isinstance(content, str):
            log.warning(f"Invalid content provided for {url}")
            return []
        
        findings = []
        
        try:
            # Find all merge/extend function calls
            merge_findings = self._find_merge_patterns(content, url)
            findings.extend(merge_findings)
            
            # Find all dynamic property access with dangerous property names
            property_findings = self._find_dangerous_property_access(content, url)
            findings.extend(property_findings)
            
            # Find recursive functions with object merging
            recursive_findings = self._find_recursive_merging(content, url)
            findings.extend(recursive_findings)
            
            if findings:
                log.info(f"Found {len(findings)} potential prototype pollution vulnerabilities in {url}")
            
            return findings
            
        except Exception as e:
            log.error(f"Error scanning content from {url}: {e}", exc_info=True)
            return []
    
    def _find_merge_patterns(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Find merge/extend function calls that could lead to prototype pollution.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of findings
        """
        findings = []
        
        try:
            for pattern in self.merge_patterns:
                for match in pattern.finditer(content):
                    try:
                        # Check if there's user input nearby
                        context_start = max(0, match.start() - DEFAULT_CONTEXT_SIZE)
                        context_end = min(len(content), match.end() + DEFAULT_CONTEXT_SIZE)
                        context_content = content[context_start:context_end]
                        
                        has_user_input = any(source.search(context_content) for source in self.input_source_patterns)
                        
                        # Check if there are dangerous property names nearby
                        has_dangerous_props = any(prop.search(context_content) for prop in self.dangerous_property_patterns)
                        
                        # Only report if there's potential user input or dangerous properties
                        if has_user_input or has_dangerous_props:
                            line_number = content.count('\n', 0, match.start()) + 1
                            
                            # Get a smaller context for display
                            display_context_start = max(0, match.start() - DEFAULT_DISPLAY_CONTEXT_SIZE)
                            display_context_end = min(len(content), match.end() + DEFAULT_DISPLAY_CONTEXT_SIZE)
                            display_context = content[display_context_start:display_context_end]
                            
                            severity = "high" if has_user_input and has_dangerous_props else "medium"
                            
                            findings.append({
                                'type': 'vulnerability',
                                'vulnerability_type': 'prototype_pollution',
                                'match': match.group(0),
                                'line': line_number,
                                'url': url,
                                'context': display_context,
                                'severity': severity,
                                'has_user_input': has_user_input,
                                'has_dangerous_props': has_dangerous_props,
                                'confidence': 0.8 if has_user_input and has_dangerous_props else 0.6,
                                'description': f"Potential prototype pollution in object merging function" + 
                                              (" with user input" if has_user_input else "") +
                                              (" and dangerous property names" if has_dangerous_props else "")
                            })
                    except Exception as e:
                        log.error(f"Error processing merge pattern match in {url}: {e}", exc_info=True)
                        continue
            
            return findings
            
        except Exception as e:
            log.error(f"Error finding merge patterns in {url}: {e}", exc_info=True)
            return []
    
    def _find_dangerous_property_access(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Find dangerous property access patterns that could lead to prototype pollution.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of findings
        """
        findings = []
        
        try:
            for pattern in self.dynamic_property_patterns:
                for match in pattern.finditer(content):
                    try:
                        # Check if there's user input nearby
                        context_start = max(0, match.start() - DEFAULT_CONTEXT_SIZE)
                        context_end = min(len(content), match.end() + DEFAULT_CONTEXT_SIZE)
                        context_content = content[context_start:context_end]
                        
                        has_user_input = any(source.search(context_content) for source in self.input_source_patterns)
                        
                        # Only report if there's potential user input
                        if has_user_input:
                            line_number = content.count('\n', 0, match.start()) + 1
                            
                            # Get a smaller context for display
                            display_context_start = max(0, match.start() - DEFAULT_DISPLAY_CONTEXT_SIZE)
                            display_context_end = min(len(content), match.end() + DEFAULT_DISPLAY_CONTEXT_SIZE)
                            display_context = content[display_context_start:display_context_end]
                            
                            findings.append({
                                'type': 'vulnerability',
                                'vulnerability_type': 'prototype_pollution',
                                'match': match.group(0),
                                'line': line_number,
                                'url': url,
                                'context': display_context,
                                'severity': "medium",
                                'has_user_input': True,
                                'confidence': 0.7,
                                'description': "Potential prototype pollution in dynamic property access with user input"
                            })
                    except Exception as e:
                        log.error(f"Error processing property access match in {url}: {e}", exc_info=True)
                        continue
            
            return findings
            
        except Exception as e:
            log.error(f"Error finding dangerous property access in {url}: {e}", exc_info=True)
            return []
    
    def _find_recursive_merging(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Find recursive functions with object merging that could lead to prototype pollution.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of findings
        """
        findings = []
        
        try:
            for pattern in self.recursive_patterns:
                for match in pattern.finditer(content):
                    try:
                        # Check if there's object merging in the function
                        context_start = match.start()
                        context_end = min(len(content), match.end() + DEFAULT_RECURSION_LOOKAHEAD)
                        context_content = content[context_start:context_end]
                        
                        has_merge = any(merge.search(context_content) for merge in self.merge_patterns)
                        
                        # Check if there's user input
                        has_user_input = any(source.search(context_content) for source in self.input_source_patterns)
                        
                        # Only report if there's both recursion and merging
                        if has_merge:
                            line_number = content.count('\n', 0, match.start()) + 1
                            
                            # Get a smaller context for display
                            display_context_start = max(0, match.start() - DEFAULT_DISPLAY_CONTEXT_SIZE)
                            display_context_end = min(len(content), match.end() + DEFAULT_DISPLAY_CONTEXT_SIZE)
                            display_context = content[display_context_start:display_context_end]
                            
                            severity = "high" if has_user_input else "medium"
                            
                            findings.append({
                                'type': 'vulnerability',
                                'vulnerability_type': 'prototype_pollution',
                                'match': match.group(0)[:100] + ('...' if len(match.group(0)) > 100 else ''),
                                'line': line_number,
                                'url': url,
                                'context': display_context,
                                'severity': severity,
                                'has_user_input': has_user_input,
                                'confidence': 0.8 if has_user_input else 0.6,
                                'description': "Potential prototype pollution in recursive function with object merging" +
                                              (" and user input" if has_user_input else "")
                            })
                    except Exception as e:
                        log.error(f"Error processing recursive pattern match in {url}: {e}", exc_info=True)
                        continue
            
            return findings
            
        except Exception as e:
            log.error(f"Error finding recursive merging in {url}: {e}", exc_info=True)
            return []

# Contents of jsmon-ng/jsmon_ng/state/types.py
"""
Type definitions and data models for JSMon-NG state management.

This module provides:
- Enums for finding and error types
- Pydantic models for state management
- Type aliases for common collections
"""

import json
import logging
import os
import re
import time
from datetime import datetime, timezone
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from pydantic import (
    BaseModel, Field, PrivateAttr, HttpUrl, ValidationError,
    model_validator, field_validator
)

logger = logging.getLogger(__name__)

# --- Helper Functions ---
def ensure_timezone_aware(v: Any) -> Optional[datetime]:
    """Ensure a timestamp is timezone-aware."""
    if v is None:
        return None
    if isinstance(v, str):
        try:
            v = datetime.fromisoformat(v)
        except ValueError as e:
            raise ValueError(f"Invalid ISO timestamp format: {v}") from e
    if isinstance(v, datetime):
        if v.tzinfo is None:
            return v.replace(tzinfo=timezone.utc)
        return v
    raise ValueError("Timestamp must be a datetime object or valid ISO string")

# --- Base Model ---
class _BaseModel(BaseModel):
    """Base model with common configuration."""
    model_config = {
        "frozen": True,
        "extra": "forbid",
        "validate_assignment": True,
        "json_encoders": {datetime: lambda v: v.isoformat()}
    }


# --- Enums ---
class FindingType(str, Enum):
    """Types of findings that can be detected in JavaScript files."""
    SECRET = "secret"
    PATTERN = "pattern"
    ENDPOINT = "endpoint"
    SOURCEMAP_ERROR = "sourcemap_error"
    VULNERABILITY = "vulnerability"
    LINK = "link"

class ErrorType(str, Enum):
    """Types of errors that can occur during processing."""
    HTTP = "http"
    PROCESSING = "processing"
    SAVE = "save"
    SEMAPHORE_TIMEOUT = "semaphore_timeout"
    INTERNAL = "internal"

# --- Base Models ---
class Finding(_BaseModel):
    """A finding detected in a JavaScript file."""
    type: FindingType
    reason: str
    line: int
    preview: str
    severity: int = Field(ge=0, le=10)
    confidence: float = Field(ge=0.0, le=1.0)

class ErrorDetails(_BaseModel):
    """Details about an error that occurred during processing."""
    type: ErrorType
    message: str
    status_code: Optional[int] = None
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class HistoryEntry(_BaseModel):
    """A record of a JavaScript file's state at a point in time."""
    hash: str
    size: int
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    filename: str
    risk_score: float = Field(ge=0.0, le=100.0)
    findings: List[Finding] = Field(default_factory=list)
    errors: List[ErrorDetails] = Field(default_factory=list)

    @field_validator('timestamp', mode='before')
    @classmethod
    def ensure_timezone(cls, v: Any) -> datetime:
        """Ensure timestamp is timezone-aware."""
        result = ensure_timezone_aware(v)
        if result is None:
            raise ValueError("Timestamp cannot be None for HistoryEntry")
        return result

class History(_BaseModel):
    """A collection of historical entries for a URL."""
    model_config = {
        "frozen": False,  # Make this model mutable
        "extra": "forbid",
        "validate_assignment": True,
        "json_encoders": {datetime: lambda v: v.isoformat()}
    }
    entries: List[HistoryEntry] = Field(default_factory=list)

    @model_validator(mode='after')
    def check_history_sorted(self) -> 'History':
        entries = self.entries
        for earlier, later in zip(entries, entries[1:]):
            if later.timestamp < earlier.timestamp:
                raise ValueError("History entries must be sorted by timestamp")
        return self

    def latest(self) -> Optional[HistoryEntry]:
        return self.entries[-1] if self.entries else None

    def prune(self, keep: int) -> "History":
        if len(self.entries) <= keep:
            return self
        return self.model_copy(update={"entries": self.entries[-keep:]})

    def add_entry(self, entry: HistoryEntry) -> None:
        """Add a new history entry, maintaining chronological order."""
        self.entries.append(entry)
        # Sort entries by timestamp to maintain order
        self.entries.sort(key=lambda e: e.timestamp)

class UrlState(_BaseModel):
    """The current state of a monitored URL."""
    url: HttpUrl
    first_seen: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_checked: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_changed: Optional[datetime] = None
    consecutive_404s: int = Field(default=0, ge=0)
    history: History = Field(default_factory=History)
    current_hash: Optional[str] = None
    current_size: Optional[int] = None
    current_filename: Optional[str] = None
    current_risk_score: float = Field(default=0.0, ge=0.0, le=100.0)
    current_findings: List[Finding] = Field(default_factory=list)
    current_errors: List[ErrorDetails] = Field(default_factory=list)
    
    etag: Optional[str] = None
    last_modified: Optional[str] = None
    
    seen_link_hashes: Set[str] = Field(default_factory=set, description="Set of hashes for links that have been seen before")
    seen_secret_hashes: Set[str] = Field(default_factory=set, description="Set of hashes for secrets that have been seen before")
    seen_pattern_hashes: Set[str] = Field(default_factory=set, description="Set of hashes for patterns that have been seen before")
    seen_endpoint_hashes: Set[str] = Field(default_factory=set, description="Set of hashes for endpoints that have been seen before")
    seen_vulnerability_hashes: Set[str] = Field(default_factory=set, description="Set of hashes for vulnerabilities that have been seen before")

    model_config = {
        "frozen": False,  # Make this model mutable
        "extra": "forbid",
        "validate_assignment": True,
        "json_encoders": {datetime: lambda v: v.isoformat()},
        "json_schema_extra": {
            "examples": [{
                "url": "https://example.com/script.js",
                "first_seen": "2024-01-01T00:00:00+00:00",
                "last_checked": "2024-01-01T00:00:00+00:00",
                "history": {"entries": []},
                "seen_link_hashes": [],
                "seen_secret_hashes": [],
                "seen_pattern_hashes": [],
                "seen_endpoint_hashes": [],
                "seen_vulnerability_hashes": []
            }]
        }
    }

    @field_validator('seen_link_hashes', 'seen_secret_hashes', 'seen_pattern_hashes', 
                    'seen_endpoint_hashes', 'seen_vulnerability_hashes', mode='before')
    @classmethod
    def ensure_sets(cls, v: Any) -> Set[str]:
        """Ensure hash collections are sets."""
        if isinstance(v, list):
            return set(v)
        if isinstance(v, set):
            return v
        return set()

    @field_validator('last_checked', 'last_changed', 'first_seen', mode='before')
    @classmethod
    def ensure_timestamps_timezone_aware(cls, v: Any) -> Optional[datetime]:
        """Ensure timestamps are timezone-aware."""
        return ensure_timezone_aware(v)

    @field_validator('current_hash')
    @classmethod
    def must_be_sha256(cls, v: Optional[str]) -> Optional[str]:
        if v and not re.fullmatch(r'[a-f0-9]{64}', v):
            raise ValueError("current_hash must be a 64-char hex SHA256")
        return v

    @model_validator(mode='after')
    def validate_timestamp_order(self) -> 'UrlState':
        if self.last_checked < self.first_seen:
            raise ValueError("last_checked must be after first_seen")
        if self.last_changed is not None and self.last_changed < self.first_seen:
            raise ValueError("last_changed must be after first_seen")
        # Allow last_checked to be equal to last_changed, as they can be set at the same time.
        # if self.last_changed is not None and self.last_checked < self.last_changed:
        #     raise ValueError("last_checked must be after last_changed")
        return self

    def _get_hash_set(self, finding_type: FindingType) -> Set[str]:
        """Get the hash set for a specific finding type."""
        attr_name = f"seen_{finding_type.value}_hashes"
        return getattr(self, attr_name)

    def is_new_finding(self, finding_type: FindingType, content_hash: str) -> bool:
        """Check if a finding is new based on its hash."""
        if not content_hash:
            return True  # Empty hash is always considered new
        return content_hash not in self._get_hash_set(finding_type)

    def mark_finding_seen(self, finding_type: FindingType, content_hash: str) -> None:
        """Mark a finding as seen by adding its hash to the appropriate set."""
        if content_hash:  # Only add non-empty hashes
            self._get_hash_set(finding_type).add(content_hash)

    def mark_findings_seen(self, findings: List[Finding], hash_func=None) -> None:
        """Mark multiple findings as seen efficiently."""
        if hash_func is None:
            hash_func = lambda f: f"{f.type.value}:{f.reason}:{f.line}"

        for finding in findings:
            content_hash = hash_func(finding)
            self.mark_finding_seen(finding.type, content_hash)

    @classmethod
    def load(cls, path: Path, keep_versions: Optional[int] = None) -> 'UrlState':
        try:
            raw = path.read_text()
            data = json.loads(raw)
            if keep_versions is not None and 'history' in data:
                history_data = data.get('history', {})
                if isinstance(history_data, dict) and 'entries' in history_data:
                    data['history'] = History(**history_data).prune(keep_versions).model_dump()
                elif isinstance(history_data, History):
                     data['history'] = history_data.prune(keep_versions).model_dump()
            return cls.model_validate(data)
        except (OSError, json.JSONDecodeError, ValidationError) as e:
            raise RuntimeError(f"Could not load UrlState from {path}: {e}") from e

    def save(self, path: Path) -> None:
        """Save UrlState to file with atomic write and proper error handling."""
        max_retries = 3
        retry_delay = 0.1

        for attempt in range(max_retries):
            tmp_path = None
            try:
                tmp_path = path.with_suffix(f'.tmp.{os.getpid()}.{time.time_ns()}')
                tmp_path.write_text(self.model_dump_json(indent=2))
                os.replace(tmp_path, path)
                return
            except OSError as e:
                # Clean up temporary file if it exists
                if tmp_path and tmp_path.exists():
                    try:
                        tmp_path.unlink()
                    except OSError:
                        pass  # Ignore cleanup errors

                if attempt == max_retries - 1:
                    raise RuntimeError(f"Could not save UrlState to {path} after {max_retries} attempts: {e}") from e
                time.sleep(retry_delay * (2**attempt))

class UrlStats(_BaseModel):
    """Statistics for a single URL processed in a cycle."""
    # Override model_config to make this specific model mutable
    model_config = {
        "frozen": False,
        "extra": "forbid",
        "validate_assignment": True,
        "json_encoders": {datetime: lambda v: v.isoformat()}
    }
    url: HttpUrl 
    changed: bool = False
    is_new: bool = False
    had_error: bool = False
    risk_score: float = Field(default=0.0, ge=0.0, le=100.0)
    findings: List[Finding] = Field(default_factory=list)
    errors: List[ErrorDetails] = Field(default_factory=list)

class CycleStats(_BaseModel):
    """Statistics collected during a monitoring cycle."""
    # Override model_config to make this specific model mutable
    model_config = {
        "frozen": False,
        "extra": "forbid",
        "validate_assignment": True,
        "json_encoders": {datetime: lambda v: v.isoformat()}
    }
    start_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    end_time: Optional[datetime] = None
    urls_checked: int = Field(default=0, ge=0)
    urls_changed: int = Field(default=0, ge=0)
    urls_new: int = Field(default=0, ge=0)
    urls_errors: int = Field(default=0, ge=0)
    urls_pruned: int = Field(default=0, ge=0)
    secrets_found: int = Field(default=0, ge=0)
    patterns_found: int = Field(default=0, ge=0)
    endpoints_found: int = Field(default=0, ge=0)
    links_found: int = Field(default=0, ge=0)
    vulnerabilities_found: int = Field(default=0, ge=0)
    total_risk_score: float = Field(default=0.0, ge=0.0)
    url_stats: Dict[str, UrlStats] = Field(default_factory=dict)

    def complete(self) -> None:
        self.end_time = datetime.now(timezone.utc)

    @property
    def duration_seconds(self) -> float:
        if self.end_time is None or self.start_time is None: 
            return 0.0
        return (self.end_time - self.start_time).total_seconds()

class SummaryStats(_BaseModel): # This can remain frozen if not incrementally updated
    """Statistics for the summary report."""
    total_urls: int = Field(default=0, ge=0)
    error_urls: int = Field(default=0, ge=0)
    secrets_count: int = Field(default=0, ge=0)
    patterns_count: int = Field(default=0, ge=0)
    endpoints_count: int = Field(default=0, ge=0)
    links_count: int = Field(default=0, ge=0)
    vulnerabilities_count: int = Field(default=0, ge=0)

    @property
    def total_findings(self) -> int:
        return (
            self.secrets_count +
            self.patterns_count +
            self.endpoints_count +
            self.links_count +
            self.vulnerabilities_count
        )

class FullState(_BaseModel):
    """Complete application state."""
    urls: Dict[str, UrlState] = Field(default_factory=dict) 
    last_cycle: Optional[CycleStats] = None
    _state_file_path: Optional[Path] = PrivateAttr(None)

    model_config = {
        "frozen": False,  # Make this model mutable
        "extra": "forbid",
        "validate_assignment": True,
        "json_encoders": {datetime: lambda v: v.isoformat()}
    }

    @model_validator(mode='after')
    def check_keys_match_url(self) -> 'FullState':
        for key, value in self.urls.items():
            if key != str(value.url):
                raise ValueError(f"URL key '{key}' doesn't match value URL '{value.url}'")
        return self

    @classmethod
    def load(cls, path: Path, keep_versions: Optional[int] = None) -> 'FullState':
        """Load state from file with proper error handling and history pruning."""
        try:
            if not path.exists():
                return cls()
                
            raw = path.read_text()
            data = json.loads(raw)
            
            # Create a new dictionary for URLs to store potentially modified UrlState objects
            updated_urls: Dict[str, UrlState] = {}
            
            # Process each URL state
            for url_str, url_data in data.get("urls", {}).items():
                try:
                    # Create UrlState object with proper set handling
                    url_state = UrlState.model_validate(url_data)
                    
                    # Prune history if needed
                    if keep_versions is not None and url_state.history:
                        pruned_history = url_state.history.prune(keep_versions)
                        if pruned_history is not url_state.history:
                            url_state_dict = url_state.model_dump()
                            url_state_dict['history'] = pruned_history.model_dump()
                            url_state = UrlState.model_validate(url_state_dict)
                    
                    updated_urls[url_str] = url_state
                except ValidationError as e:
                    logger.error(f"Skipping URL '{url_str}' due to validation error: {e}")
                    continue
            
            # Create state with processed URLs
            state = cls(urls=updated_urls)
            
            # Process last cycle if present
            if 'last_cycle' in data and data['last_cycle']:
                try:
                    state.last_cycle = CycleStats.model_validate(data['last_cycle'])
                except ValidationError as e:
                    logger.error(f"Could not load last cycle stats: {e}")
            
            state._state_file_path = path
            return state
            
        except (OSError, json.JSONDecodeError) as e:
            logger.error(f"Error loading state from {path}: {e}")
            return cls()
        except Exception as e:
            logger.error(f"Unexpected error loading state from {path}: {e}")
            return cls()

    def save(self, path: Optional[Path] = None) -> None:
        """Save state to file with atomic write and retries."""
        target_path = path or self._state_file_path
        if target_path is None:
            raise RuntimeError("No path provided and no stored path available for saving state")

        max_retries = 3
        retry_delay = 0.1
        
        for attempt in range(max_retries):
            tmp_path = None
            try:
                # Create temporary file with unique name
                tmp_path = target_path.with_suffix(f'.tmp.{os.getpid()}.{time.time_ns()}')

                # Convert state to JSON with proper set handling
                state_dict = self.model_dump()

                # Write to temporary file
                tmp_path.write_text(json.dumps(state_dict, indent=2))

                # Atomic replace
                os.replace(tmp_path, target_path)

                # Update stored path
                self._state_file_path = target_path
                return

            except OSError as e:
                # Clean up temporary file if it exists
                if tmp_path and tmp_path.exists():
                    try:
                        tmp_path.unlink()
                    except OSError:
                        pass  # Ignore cleanup errors

                if attempt == max_retries - 1:
                    raise RuntimeError(f"Could not save state to {target_path} after {max_retries} attempts: {e}") from e
                time.sleep(retry_delay * (2**attempt))

    def add_cycle(self, stats: CycleStats) -> None:
        """Add cycle statistics to state."""
        self.last_cycle = stats
        if self._state_file_path:
            self.save()

    @classmethod
    def export_schema(cls, path: Path) -> None:
        schema_output = cls.model_json_schema()
        path.write_text(json.dumps(schema_output, indent=2))

CycleStats.model_rebuild()
UrlState.model_rebuild() 

UrlStateDict = Dict[str, UrlState]
UrlStatsDict = Dict[str, UrlStats]
FindingList = List[Finding]
ErrorDetailsDict = Dict[str, ErrorDetails]
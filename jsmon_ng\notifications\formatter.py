"""Notification formatting utilities for JSMon-NG.

This module provides:
- Enhanced notification formatting
- Common formatting patterns
- HTML/Markdown conversion utilities
"""

from datetime import datetime
from typing import List, Tuple

from jsmon_ng.state.types import Finding, HistoryEntry

class EnhancedNotificationFormatter:
    """Enhanced notification formatting with HTML/Markdown support."""

    @staticmethod
    def format_timestamp(dt: datetime) -> str:
        """Format a timestamp for notifications."""
        return dt.strftime("%Y-%m-%d %H:%M:%S UTC")

    @staticmethod
    def format_finding(finding: Finding) -> str:
        """Format a finding for notifications."""
        lines = [
            f"🔍 **{finding.type.value.title()}**",
            f"📝 {finding.reason}",
            f"📄 Line {finding.line}:",
            f"```javascript\n{finding.preview}\n```",
            f"⚠️ Severity: {finding.severity}/10",
            f"🎯 Confidence: {finding.confidence:.1%}",
        ]
        return "\n".join(lines)

    @staticmethod
    def format_history_entry(entry: HistoryEntry) -> str:
        """Format a history entry for notifications."""
        lines = [
            f"📅 {EnhancedNotificationFormatter.format_timestamp(entry.timestamp)}",
            f"📦 Size: {entry.size:,} bytes",
            f"🔑 Hash: {entry.hash[:8]}...",
            f"⚠️ Risk Score: {entry.risk_score}/100",
        ]
        if entry.findings:
            lines.append("\n🔍 Findings:")
            for finding in entry.findings:
                lines.append(f"\n{EnhancedNotificationFormatter.format_finding(finding)}")
        if entry.errors:
            lines.append("\n❌ Errors:")
            for error in entry.errors:
                lines.append(f"- {error.type.value}: {error.message}")
        return "\n".join(lines)

    @staticmethod
    def _format_diff_changes(
        old_entry: HistoryEntry,
        new_entry: HistoryEntry,
        is_html: bool = False,
    ) -> List[str]:
        """Format diff changes for findings and errors."""
        lines = []

        # Add findings diff
        if old_entry.findings or new_entry.findings:
            header = "<b>🔍 Findings Changes:</b>" if is_html else "🔍 Findings Changes:"
            lines.append(header)

            old_findings = {f.reason: f for f in old_entry.findings}
            new_findings = {f.reason: f for f in new_entry.findings}

            # Group findings by type
            old_by_type = {}
            new_by_type = {}
            for f in old_entry.findings:
                old_by_type.setdefault(f.type.value, []).append(f)
            for f in new_entry.findings:
                new_by_type.setdefault(f.type.value, []).append(f)

            # Show changes by type
            for finding_type in set(old_by_type.keys()) | set(new_by_type.keys()):
                old_count = len(old_by_type.get(finding_type, []))
                new_count = len(new_by_type.get(finding_type, []))
                if old_count != new_count:
                    lines.append(f"  {finding_type.title()}: {old_count} → {new_count}")

            # Removed findings
            for reason in old_findings:
                if reason not in new_findings:
                    lines.append(f"❌ Removed: {reason}")

            # Added findings
            for reason in new_findings:
                if reason not in old_findings:
                    lines.append(f"✅ Added: {reason}")

            # Modified findings
            for reason in old_findings:
                if reason in new_findings:
                    old_f = old_findings[reason]
                    new_f = new_findings[reason]
                    if old_f.severity != new_f.severity or old_f.confidence != new_f.confidence:
                        lines.append(f"🔄 Modified: {reason}")
                        if old_f.severity != new_f.severity:
                            lines.append(f"  Severity: {old_f.severity} → {new_f.severity}")
                        if old_f.confidence != new_f.confidence:
                            lines.append(f"  Confidence: {old_f.confidence:.1%} → {new_f.confidence:.1%}")

        # Add errors diff
        if old_entry.errors or new_entry.errors:
            header = "<b>❌ Errors Changes:</b>" if is_html else "❌ Errors Changes:"
            lines.append(header)

            old_errors = {e.message: e for e in old_entry.errors}
            new_errors = {e.message: e for e in new_entry.errors}

            # Group errors by type
            old_by_type = {}
            new_by_type = {}
            for e in old_entry.errors:
                old_by_type.setdefault(e.type.value, []).append(e)
            for e in new_entry.errors:
                new_by_type.setdefault(e.type.value, []).append(e)

            # Show changes by type
            for error_type in set(old_by_type.keys()) | set(new_by_type.keys()):
                old_count = len(old_by_type.get(error_type, []))
                new_count = len(new_by_type.get(error_type, []))
                if old_count != new_count:
                    lines.append(f"  {error_type.title()}: {old_count} → {new_count}")

            # Removed errors
            for msg in old_errors:
                if msg not in new_errors:
                    lines.append(f"✅ Resolved: {msg}")

            # Added errors
            for msg in new_errors:
                if msg not in old_errors:
                    lines.append(f"❌ New Error: {msg}")

        return lines

    @staticmethod
    def format_diff_notification(
        old_entry: HistoryEntry,
        new_entry: HistoryEntry,
        url: str,
    ) -> Tuple[str, str]:
        """Format a diff notification with HTML and plain text versions."""
        # Calculate size change
        size_diff = new_entry.size - old_entry.size
        size_change = f"{size_diff:+,} bytes" if size_diff != 0 else "No change"

        # Build the HTML version
        html_lines = [
            f"<b>🔄 Change Detected</b>",
            f"<a href='{url}'>{url}</a>",
            f"<b>Old Version ({EnhancedNotificationFormatter.format_timestamp(old_entry.timestamp)})</b>",
            f"Size: {old_entry.size:,} bytes",
            f"Hash: {old_entry.hash[:8]}...",
            f"Risk Score: {old_entry.risk_score:.2f}/100",
            f"<b>New Version ({EnhancedNotificationFormatter.format_timestamp(new_entry.timestamp)})</b>",
            f"Size: {new_entry.size:,} bytes",
            f"Hash: {new_entry.hash[:8]}...",
            f"Risk Score: {new_entry.risk_score:.2f}/100",
            f"<b>Size Change:</b> {size_change}",
        ]

        # Add diff changes
        html_lines.extend(EnhancedNotificationFormatter._format_diff_changes(old_entry, new_entry, is_html=True))

        # Build the plain text version
        text_lines = [
            "🔄 Change Detected",
            url,
            f"Old Version ({EnhancedNotificationFormatter.format_timestamp(old_entry.timestamp)})",
            f"Size: {old_entry.size:,} bytes",
            f"Hash: {old_entry.hash[:8]}...",
            f"Risk Score: {old_entry.risk_score:.2f}/100",
            f"New Version ({EnhancedNotificationFormatter.format_timestamp(new_entry.timestamp)})",
            f"Size: {new_entry.size:,} bytes",
            f"Hash: {new_entry.hash[:8]}...",
            f"Risk Score: {new_entry.risk_score:.2f}/100",
            f"Size Change: {size_change}",
        ]

        # Add diff changes
        text_lines.extend(EnhancedNotificationFormatter._format_diff_changes(old_entry, new_entry, is_html=False))

        return "\n".join(html_lines), "\n".join(text_lines)
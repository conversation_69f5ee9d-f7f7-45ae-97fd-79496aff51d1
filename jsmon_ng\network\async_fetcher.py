"""Asynchronous HTTP client for JSMon-NG.

This module provides an asynchronous HTTP client for fetching JavaScript files
with features like:
- Automatic retries with exponential backoff
- Circuit breaking per host
- Concurrent request limiting
- Timeout handling
- Graceful shutdown
"""

import hashlib
import logging
import asyncio
import time
import httpx
from typing import Optional, Dict, Any, Set
from urllib.parse import urlparse

from jsmon_ng.config.config import AppConfig, FetcherConfig
from .common import Fetch<PERSON><PERSON>ult
from .circuit_breaker import CircuitBreaker

log = logging.getLogger(__name__)

class AsyncClient:
    """Async HTTP client with circuit breaker and retry logic."""
    
    def __init__(self, cfg: AppConfig):
        self.app_config = cfg
        self.fetch_cfg: FetcherConfig = cfg.fetcher
        self.log = logging.getLogger(__name__)
        
        cb_cfg = cfg.network.circuit_breaker
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=cb_cfg.failure_threshold,
            recovery_timeout=cb_cfg.recovery_timeout,
            half_open_timeout=cb_cfg.half_open_timeout
        )
        
        timeout = httpx.Timeout(
            connect=self.fetch_cfg.connect_timeout,
            read=self.fetch_cfg.read_timeout,
            write=self.fetch_cfg.read_timeout,
            pool=self.fetch_cfg.timeout # This is the total timeout for one request by httpx
        )
        
        limits = httpx.Limits(
            max_connections=self.fetch_cfg.max_connections,
            max_keepalive_connections=self.fetch_cfg.max_keepalive
        )
        
        self.client = httpx.AsyncClient(
            timeout=timeout,
            limits=limits,
            http2=self.fetch_cfg.http2_enabled,
            verify=self.fetch_cfg.verify_ssl,
            trust_env=self.fetch_cfg.trust_env,
            follow_redirects=True,
            max_redirects=self.fetch_cfg.max_redirects,
            headers=self.fetch_cfg.headers.copy()
        )
        
        self.log.debug(
            f"Initialized AsyncClient with settings:\n"
            f"  Timeouts (connect/read/pool): {self.fetch_cfg.connect_timeout}s / {self.fetch_cfg.read_timeout}s / {self.fetch_cfg.timeout}s\n"
            f"  Limits: max_connections={self.fetch_cfg.max_connections}, max_keepalive={self.fetch_cfg.max_keepalive}\n"
            f"  HTTP/2: {self.fetch_cfg.http2_enabled}, SSL Verify: {self.fetch_cfg.verify_ssl}, Max Redirects: {self.fetch_cfg.max_redirects}"
        )
        
    async def close(self) -> None:
        if hasattr(self, 'client') and self.client:
            await self.client.aclose()
        log.debug("Closed AsyncClient.")

class AsyncFetcher:
    """Asynchronous URL fetcher with retry logic and circuit breaker."""
    
    def __init__(self, cfg: AppConfig):
        self.app_config = cfg
        self.fetch_cfg: FetcherConfig = cfg.fetcher
        self.log = logging.getLogger(__name__)
        self.http_client = AsyncClient(cfg)
        
        self._active_requests: Set[asyncio.Task] = set()
        self._is_closing = False
        
        self.domain_semaphore = asyncio.Semaphore(cfg.concurrency.max_concurrent_per_domain)
        
        self.log.debug(
            f"Initialized AsyncFetcher with settings:\n"
            f"  App Retries: {self.fetch_cfg.retries}, App Retry Delay: {self.fetch_cfg.retry_delay}s\n"
            f"  Max Concurrent Per Domain (Semaphore): {cfg.concurrency.max_concurrent_per_domain}"
        )
        
    async def __aenter__(self) -> 'AsyncFetcher':
        return self
        
    async def __aexit__(self, *_: Any) -> None:
        await self.close()
        
    async def close(self) -> None:
        if self._is_closing:
            return
        self._is_closing = True
        self.log.info("Closing AsyncFetcher, cancelling pending requests...") # Changed to INFO
        
        tasks_to_cancel = list(self._active_requests)
        if tasks_to_cancel:
            for task in tasks_to_cancel:
                if not task.done():
                    task.cancel()
            await asyncio.gather(*tasks_to_cancel, return_exceptions=True)
            
        await self.http_client.close()
        self._active_requests.clear()
        self._is_closing = False
        self.log.info("AsyncFetcher closed.") # Changed to INFO
            
    def _get_host(self, url: str) -> str:
        return urlparse(url).netloc
        
    async def fetch(self, url: str, previous_etag: Optional[str] = None, previous_last_modified: Optional[str] = None) -> FetchResult:
        current_task = asyncio.current_task()
        if current_task:  # Add task to active requests only if it's a valid task object
            self._active_requests.add(current_task)

        try:
            if self._is_closing:
                return FetchResult(
                    text=None, sha256=None, size=None, headers=None, status=0,
                    success=False, error={"type": "fetcher_closing", "message": "Fetcher is closing"},
                    duration=0
                )
                
            host = self._get_host(url)
            overall_start_time = time.time()
            
            if not self.http_client.circuit_breaker.allow_request(host):
                self.log.warning(f"Circuit breaker open for {host}, skipping request to {url}")
                return FetchResult(
                    text=None, sha256=None, size=None, headers=None, status=0,
                    success=False, error={"type": "circuit_breaker", "message": f"Circuit open for {host}"},
                    duration=time.time() - overall_start_time
                )
            
            conditional_headers: Dict[str, str] = {}
            if previous_etag: conditional_headers['If-None-Match'] = previous_etag
            if previous_last_modified: conditional_headers['If-Modified-Since'] = previous_last_modified

            async with self.domain_semaphore:
                for attempt in range(1, self.fetch_cfg.retries + 1):
                    if self._is_closing:
                        self.log.debug(f"Fetch for {url} aborted due to fetcher closing (attempt {attempt}).")
                        return FetchResult(
                            text=None, sha256=None, size=None, headers=None, status=0,
                            success=False, error={"type": "fetcher_closing", "message": "Fetcher closing during retry"},
                            duration=time.time() - overall_start_time
                        )
                    
                    attempt_start_time_inner = time.time()
                    self.log.debug(f"Attempt {attempt}/{self.fetch_cfg.retries} to fetch {url}")
                    
                    try:
                        response = await self.http_client.client.get(url, headers=conditional_headers)
                        attempt_duration = time.time() - attempt_start_time_inner

                        if response.status_code == 304:
                            self.http_client.circuit_breaker.on_success(host)
                            self.log.debug(f"Resource not modified (304) for {url} in {attempt_duration:.2f}s (attempt {attempt})")
                            return FetchResult(
                                text=None, sha256=None, size=None, headers=dict(response.headers),
                                status=304, success=True, error=None, duration=attempt_duration
                            )
                        
                        response.raise_for_status() 

                        content_bytes = response.content 
                        sha256_hash = hashlib.sha256(content_bytes).hexdigest()
                        try:
                            text_content = content_bytes.decode('utf-8')
                        except UnicodeDecodeError:
                            self.log.warning(f"Could not decode content from {url} as UTF-8. Trying latin-1.")
                            text_content = content_bytes.decode('latin-1', errors='replace')

                        self.http_client.circuit_breaker.on_success(host)
                        self.log.debug(f"Successfully fetched {url} in {attempt_duration:.2f}s (attempt {attempt})")
                        return FetchResult(
                            text=text_content, sha256=sha256_hash, size=len(content_bytes),
                            headers=dict(response.headers), status=response.status_code,
                            success=True, error=None, duration=attempt_duration
                        )

                    except httpx.TimeoutException as e:
                        self.http_client.circuit_breaker.on_failure(host, e)
                        self.log.warning(f"Attempt {attempt}/{self.fetch_cfg.retries} for {url} timed out ({type(e).__name__}): {e}")
                        if attempt == self.fetch_cfg.retries:
                            return FetchResult(
                                text=None, sha256=None, size=None, headers=None, status=0,
                                success=False, error={"type": type(e).__name__, "message": str(e)},
                                duration=time.time() - overall_start_time
                            )
                    except httpx.HTTPStatusError as e:
                        self.http_client.circuit_breaker.on_failure(host, e)
                        self.log.warning(f"Attempt {attempt}/{self.fetch_cfg.retries} for {url} failed with HTTP {e.response.status_code}: {e.request.url}")
                        if attempt == self.fetch_cfg.retries or not (500 <= e.response.status_code < 600 or (e.response.status_code == 429 and self.fetch_cfg.retry_429_respect_retry_after)):
                            return FetchResult(
                                text=None, sha256=None, size=None,
                                headers=dict(e.response.headers) if hasattr(e, 'response') and e.response else None,
                                status=e.response.status_code if hasattr(e, 'response') and e.response else 0,
                                success=False,
                                error={"type": "http_status_error", "message": str(e), "status_code": e.response.status_code if hasattr(e, 'response') and e.response else 0},
                                duration=time.time() - overall_start_time
                            )
                    except httpx.RequestError as e: 
                        self.http_client.circuit_breaker.on_failure(host, e)
                        self.log.warning(f"Attempt {attempt}/{self.fetch_cfg.retries} for {url} failed with RequestError ({type(e).__name__}): {e}")
                        if attempt == self.fetch_cfg.retries:
                            return FetchResult(
                                text=None, sha256=None, size=None, headers=None, status=0,
                                success=False, error={"type": type(e).__name__, "message": str(e)},
                                duration=time.time() - overall_start_time
                            )
                    except Exception as e: 
                        self.http_client.circuit_breaker.on_failure(host, e)
                        self.log.error(f"Unexpected error during fetch attempt {attempt} for {url}: {e}", exc_info=True)
                        if attempt == self.fetch_cfg.retries:
                            return FetchResult(
                                text=None, sha256=None, size=None, headers=None, status=0,
                                success=False, error={"type": "unexpected_fetch_error", "message": str(e)},
                                duration=time.time() - overall_start_time
                            )
                        
                    if attempt < self.fetch_cfg.retries:
                        delay = self.fetch_cfg.retry_delay * (2**(attempt -1)) 
                        self.log.debug(f"Retrying {url} in {delay:.2f}s...")
                        try:
                            await asyncio.sleep(delay)
                        except asyncio.CancelledError:
                            self.log.info(f"Sleep cancelled during retry for {url}, likely shutdown.")
                            # Propagate cancellation or return error
                            raise # Re-raise CancelledError to be caught by outer handler
        
        except asyncio.CancelledError:
            self.log.warning(f"Fetch operation for {url} was cancelled (during semaphore or shutdown).")
            return FetchResult(
                text=None, sha256=None, size=None, headers=None, status=0,
                success=False, error={"type": "cancelled", "message": "Fetch operation cancelled"},
                duration=time.time() - overall_start_time
            )
        finally:
            if current_task: 
                self._active_requests.discard(current_task)
        
        self.log.error(f"All {self.fetch_cfg.retries} fetch attempts failed for {url} after {time.time() - overall_start_time:.2f}s overall (unexpected loop exit).")
        return FetchResult(
            text=None, sha256=None, size=None, headers=None, status=0,
            success=False, error={"type": "retries_exhausted_unexpected", "message": f"All {self.fetch_cfg.retries} attempts failed (unexpected)"},
            duration=time.time() - overall_start_time
        )
"""Configuration models for JSMon-NG.

This module defines the configuration structure using Pydantic models.
The main AppConfig class is used to validate and store all application settings.
"""

import os
import re
import yaml
from pathlib import Path
from typing import List, Dict, Any, Optional, Pattern, Literal
from pydantic import BaseModel, Field, HttpUrl, model_validator, PrivateAttr

class _BaseModel(BaseModel):
    """Base model with common configuration."""
    # Pydantic v2 uses model_config
    model_config = {
        "extra": "forbid",
        "validate_assignment": True,
    }

class PrettifyConfig(_BaseModel):
    """Configuration for code prettification."""
    enabled: bool = True
    prettier_path: str = "prettier"
    timeout: int = 10
    skip_html_content: bool = True

class DifferConfig(_BaseModel):
    """Configuration for diff generation."""
    prettify_before_diff: PrettifyConfig = Field(default_factory=PrettifyConfig)
    ignore_whitespace_only_changes: bool = True
    min_diff_lines: int = 1
    diff_context: int = 3
    native_diff_threshold: int = 5000
    ignore_line_patterns: List[str] = Field(default_factory=list, description="Regex patterns for lines to ignore in diffs")
    
    _compiled_patterns: List[Pattern[str]] = PrivateAttr(default_factory=list)

    @model_validator(mode='after')
    def compile_ignore_patterns_val(self) -> 'DifferConfig':
        """Pre-compile regex patterns for better performance."""
        compiled = []
        for pattern_str in self.ignore_line_patterns:
            try:
                compiled.append(re.compile(pattern_str))
            except re.error as err:
                raise ValueError(f"Invalid diff ignore pattern '{pattern_str}': {err}")
        self._compiled_patterns = compiled
        return self
    
    @property
    def compiled_patterns(self) -> List[Pattern[str]]:
        return self._compiled_patterns


class LinkExtractionConfig(_BaseModel):
    """Configuration for link extraction."""
    enabled: bool = True
    patterns: List[str] = Field(default_factory=list, description="Custom regex patterns for link extraction. If empty, defaults are used.")
    exclude_patterns: List[str] = Field(default_factory=list)
    notify_new_links: bool = False
    track_changes: bool = False
    categorize_links: bool = False
    min_url_length: int = 8
    max_url_length: int = 2048

    _compiled_exclude_patterns: List[Pattern[str]] = PrivateAttr(default_factory=list)
    _compiled_link_patterns: List[Pattern[str]] = PrivateAttr(default_factory=list)

    @model_validator(mode='after')
    def compile_patterns_val(self) -> 'LinkExtractionConfig':
        """Compile regex patterns for link exclusion and extraction."""
        # Compile exclude_patterns
        self._compiled_exclude_patterns = [
            re.compile(pattern, re.IGNORECASE)
            for pattern in self.exclude_patterns
        ]
        # Compile custom link patterns (if any)
        compiled_custom_links = []
        for pattern_str in self.patterns:
            try:
                compiled_custom_links.append(re.compile(pattern_str, re.IGNORECASE))
            except re.error as err:
                raise ValueError(f"Invalid link extraction pattern '{pattern_str}': {err}")
        self._compiled_link_patterns = compiled_custom_links
        return self

    @property
    def compiled_custom_link_patterns(self) -> List[Pattern[str]]:
        return self._compiled_link_patterns

    @property
    def compiled_ignore_patterns(self) -> List[Pattern[str]]:
        return self._compiled_exclude_patterns
        
    def should_ignore_link(self, url: str) -> bool:
        """Check if a URL should be ignored based on exclude patterns."""
        return any(pattern.search(url) for pattern in self._compiled_exclude_patterns)


class VulnerabilityDetectionConfig(_BaseModel):
    """Configuration for vulnerability detection."""
    enabled: bool = True
    detectors: List[str] = Field(default_factory=lambda: [
        'prototype_pollution',
        'template_injection'
    ])
    min_severity: str = "medium"
    max_findings_per_file: int = 100

class TrufflehogConfig(_BaseModel):
    """Configuration for TruffleHog integration."""
    enabled: bool = True
    command: str = "trufflehog"
    timeout: int = 90
    extra_args: List[str] = Field(default_factory=lambda: ["--no-update", "--json"])
    max_concurrent_scans: int = Field(
        default=5,
        ge=1,
        le=20,
        description="Maximum number of concurrent TruffleHog scans"
    )

class ScannerConfig(_BaseModel):
    """Configuration for all scanning modules."""
    link_extraction: LinkExtractionConfig = Field(default_factory=LinkExtractionConfig)
    vulnerability_detection: VulnerabilityDetectionConfig = Field(default_factory=VulnerabilityDetectionConfig)
    trufflehog: TrufflehogConfig = Field(default_factory=TrufflehogConfig)
    basic_pattern_scanning: bool = Field(
        default=True,
        description="Enable/disable basic pattern scanning (regex-based pattern matching)"
    )

class NotificationRetryConfig(_BaseModel):
    """Configuration for notification retries."""
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: float = 30.0

class DiscordNotificationConfig(_BaseModel):
    """Configuration for Discord notifications."""
    enabled: bool = False
    webhook_url: Optional[HttpUrl] = None
    username: Optional[str] = None
    avatar_url: Optional[HttpUrl] = None
    color: int = 0x3498DB  # This is a general color for Discord embeds via this channel
    include_preview: bool = True
    max_preview_length: int = 1000

class TelegramNotificationConfig(_BaseModel):
    """Configuration for Telegram notifications."""
    enabled: bool = False
    bot_token: Optional[str] = None
    chat_id: Optional[str] = None
    timeout: float = 30.0
    parse_mode: str = "HTML"
    disable_web_page_preview: bool = True

class NotificationColorsConfig(_BaseModel):
    """Configuration for notification colors."""
    change: Optional[int] = Field(default=0xFFA500)  # Orange
    secret: Optional[int] = Field(default=0xFF0000)  # Red
    pattern: Optional[int] = Field(default=0x00FFFF)  # Cyan
    endpoint: Optional[int] = Field(default=0x778899)  # LightSlateGray
    vulnerability: Optional[int] = Field(default=0x8B0000)  # Dark red
    sourcemap: Optional[int] = Field(default=0x8A2BE2)  # BlueViolet
    error: Optional[int] = Field(default=0xDC143C)  # Crimson
    summary: Optional[int] = Field(default=0xADD8E6)  # LightBlue

class EnhancedFormattingConfig(_BaseModel):
    """Configuration for enhanced formatting settings."""
    enabled: bool = True
    use_emoji: bool = True
    use_markdown: bool = True  # For general markdown, Discord uses its own variant
    include_context: bool = True
    max_context_length: int = 200  # Defined field
    color_coded_severity: bool = True
    truncate_long_messages: bool = True
    max_message_length: int = 2000


class NotificationsConfig(_BaseModel):
    """Configuration for all notification channels."""
    notify_on_fetch_error: bool = True
    notify_on_processing_error: bool = True
    notify_on_new_content: bool = True
    notify_on_changed_content: bool = True
    notify_on_new_findings: bool = True
    discord: DiscordNotificationConfig = Field(default_factory=DiscordNotificationConfig)
    telegram: TelegramNotificationConfig = Field(default_factory=TelegramNotificationConfig)
    retry: NotificationRetryConfig = Field(default_factory=NotificationRetryConfig)
    colors: NotificationColorsConfig = Field(default_factory=NotificationColorsConfig)
    enhanced_formatting: EnhancedFormattingConfig = Field(default_factory=EnhancedFormattingConfig)


class CircuitBreakerConfig(_BaseModel):
    """Configuration for circuit breaker behavior."""
    failure_threshold: int = 5
    recovery_timeout: int = 30
    half_open_timeout: int = 10

class FetcherConfig(_BaseModel):
    """Configuration for direct httpx.AsyncClient parameters."""
    user_agent: str = "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0"
    timeout: int = 30
    connect_timeout: int = 10
    read_timeout: int = 20
    max_connections: int = 20
    max_keepalive: int = 5
    http2_enabled: bool = True
    max_redirects: int = 5
    verify_ssl: bool = True
    trust_env: bool = True
    headers: Dict[str, str] = Field(default_factory=lambda: {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "Connection": "keep-alive",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Cache-Control": "max-age=0"
    })
    
    cache_normalize_scheme: bool = Field(default=True, description="Normalize URL scheme for cache key")
    strip_query_params: bool = Field(default=False, description="Strip all query parameters for cache key")
    cache_normalize_query: bool = Field(default=True, description="Normalize query parameters (sort, remove specific) for cache key")
    strip_fragments_for_cache_key: bool = Field(default=True, description="Strip URL fragments for cache key")
    cache_remove_params: List[str] = Field(default_factory=list, description="Query parameter keys to remove during cache normalization")
    
    fetch_source_maps: bool = True
    sourcemap_timeout: int = 60

    retries: int = 3
    retry_delay: float = 1.0

    retry_404_errors: bool = False
    retry_4xx_errors: bool = False
    retry_5xx_errors: bool = True
    retry_429_respect_retry_after: bool = True
    retry_429_max_wait: int = 300

    host_semaphore_timeout: int = 60
    shutdown_timeout: int = 10
    metrics_prefix: str = "jsmon_fetch"

    cache_enabled: bool = False
    cache_name: str = "jsmon_cache"
    cache_backend: str = "sqlite"
    cache_expire_after: int = 3600

class NetworkConfig(_BaseModel):
    """Configuration for high-level network behavior policies."""
    max_retries: int = 3
    retry_delay: float = 2.0
    verify_ssl: bool = True
    timeout: float = 60.0
    connect_timeout: float = 10.0
    read_timeout: float = 50.0
    max_redirects: int = 5
    headers: Dict[str, str] = Field(default_factory=dict)
    additional_headers: Dict[str, str] = Field(default_factory=dict)
    circuit_breaker: CircuitBreakerConfig = Field(default_factory=CircuitBreakerConfig)

class ConcurrencyConfig(_BaseModel):
    """Configuration for concurrent operations."""
    max_fetch_workers: int = 10
    max_concurrent_per_domain: int = 2
    semaphore_timeout: int = 30

class LoggingConfig(_BaseModel):
    """Configuration for logging."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
    file: Optional[str] = None
    max_size: int = 10 * 1024 * 1024
    backup_count: int = 5
    handlers: Optional[Dict[str, Any]] = None  # For advanced logging config from YAML
    loggers: Optional[Dict[str, Any]] = None  # For advanced logging config from YAML
    date_format: Optional[str] = "%Y-%m-%d %H:%M:%S"


class StateManagementConfig(_BaseModel):
    """Configuration for state management and persistence."""
    max_memory_mb: float = 500.0
    memory_limit_action: Literal["warn_and_continue", "stop", "cleanup"] = "warn_and_continue"
    backup_corrupt_files: bool = True
    lock_timeout: int = 10
    summary_lock_timeout: int = 5
    atomic_state_updates: bool = True
    incremental_state_backup: bool = True
    backup_interval_hours: int = 6
    recovery_mode: Literal["auto", "manual", "disabled"] = "auto"

class CleanupConfig(_BaseModel):
    """Configuration for cleanup operations."""
    enabled: bool = True
    keep_versions: int = Field(
        default=3,
        ge=1,
        le=100,
        description="Number of previous versions to keep per URL"
    )
    cleanup_interval_hours: int = Field(default=24, description="Hours between cleanup runs")
    max_file_age_days: int = Field(default=30, description="Maximum age of files in days")

class ErrorHandlingConfig(_BaseModel):
    """Configuration for error handling and circuit breaking."""
    prune_after_consecutive_404: int = Field(
        default=5,
        ge=1,
        description="Number of consecutive 404s before pruning a URL"
    )
    circuit_breaker_enabled: bool = True
    failure_threshold: int = Field(
        default=5,
        ge=1,
        description="Number of failures before circuit breaker opens (used if not overridden by specific CB config)"
    )
    recovery_timeout: int = Field(
        default=300,
        ge=1,
        description="Seconds to wait before retrying (used if not overridden by specific CB config)"
    )
    disable_features_on_timeout: List[str] = Field(
        default_factory=lambda: ["trufflehog", "source_maps"],
        description="Features to disable when system is under load"
    )
    health_check_interval: int = Field(
        default=3600,
        ge=60,
        description="Seconds between system health checks"
    )
    max_error_rate: float = Field(default=0.5, description="Maximum error rate before disabling features")

class SeverityWeightsConfig(_BaseModel):
    """Configuration for custom severity weights."""
    type_weights: Dict[str, int] = Field(default_factory=dict)
    pattern_weights: Dict[str, int] = Field(default_factory=dict)

class AppConfig(_BaseModel):
    """Main application configuration."""
    recon_base_directory: Optional[str] = None
    monitored_targets: Optional[List[str]] = None
    target_files: List[str] = Field(
        default_factory=list,
        description="List of specific files to monitor (if empty, all files are monitored)"
    )
    state_file: str = "jsmon_state.json"
    download_directory: str = "js_files"
    run_interval_seconds: int = 300
    
    fetcher: FetcherConfig = Field(default_factory=FetcherConfig)
    concurrency: ConcurrencyConfig = Field(default_factory=ConcurrencyConfig)
    scanner: ScannerConfig = Field(default_factory=ScannerConfig)
    differ: DifferConfig = Field(default_factory=DifferConfig)
    notifications: NotificationsConfig = Field(default_factory=NotificationsConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    
    state: StateManagementConfig = Field(default_factory=StateManagementConfig)
    cleanup: CleanupConfig = Field(default_factory=CleanupConfig)
    error_handling: ErrorHandlingConfig = Field(default_factory=ErrorHandlingConfig)
    network: NetworkConfig = Field(default_factory=NetworkConfig)
    severity: Optional[SeverityWeightsConfig] = None


def _substitute_env_vars(obj: Any) -> Any:
    """Recursively substitute environment variables in configuration values."""
    if isinstance(obj, str):
        # Handle ${VAR_NAME} pattern
        if obj.startswith('${') and obj.endswith('}'):
            env_var = obj[2:-1]
            return os.getenv(env_var, obj)  # Return original if env var not found
        return obj
    elif isinstance(obj, dict):
        return {key: _substitute_env_vars(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [_substitute_env_vars(item) for item in obj]
    else:
        return obj

def load_config(config_path: str | Path) -> AppConfig:
    """Load and validate configuration from a YAML file."""
    path = Path(config_path)
    if not path.exists():
        raise FileNotFoundError(f"Config file not found: {config_path}")

    try:
        with path.open('r', encoding='utf-8') as f:
            raw_config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        raise ValueError(f"Invalid YAML in config file {config_path}: {e}")

    if raw_config is None:
        raw_config = {}

    # Substitute environment variables
    raw_config = _substitute_env_vars(raw_config)

    return AppConfig.model_validate(raw_config)
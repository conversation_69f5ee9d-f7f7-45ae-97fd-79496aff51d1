# Changelog

All notable changes to JSMon-NG will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.3.0] - 2024-01-15

### Added
- **Fully Asynchronous Architecture**: Complete rewrite with async/await for maximum performance
- **Concurrent URL Processing**: Multiple URLs processed simultaneously for better efficiency
- **Advanced HTTP Client**: HTTP/2 support, connection pooling, and enhanced configuration
- **Enhanced Error Handling**: Comprehensive error recovery and circuit breaker patterns
- **Performance Optimizations**: Native diff support, streaming processing, and memory management
- **Rich Notifications**: Enhanced Discord and Telegram integration with better formatting
- **Comprehensive Configuration**: Extensive YAML configuration with environment variable support
- **State Management**: Improved state handling with atomic updates and backup mechanisms
- **Advanced Scanning**: Enhanced secret detection, pattern matching, and vulnerability scanning
- **Diff Improvements**: Better diff generation with Prettier integration and noise reduction

### Changed
- **Breaking**: Minimum Python version increased to 3.9+
- **Breaking**: Configuration format updated for better organization
- **Breaking**: CLI interface redesigned for better usability
- **Performance**: Significantly improved memory usage and processing speed
- **Architecture**: Modular design with clear separation of concerns
- **Dependencies**: Updated to latest versions with better compatibility

### Fixed
- **Memory Leaks**: Resolved memory issues with large file processing
- **Concurrency Issues**: Fixed race conditions in state management
- **Network Reliability**: Improved handling of network timeouts and failures
- **State Corruption**: Enhanced state file integrity and recovery mechanisms

### Security
- **Input Validation**: Enhanced validation for all user inputs
- **Dependency Updates**: Updated all dependencies to latest secure versions
- **Secret Handling**: Improved secret detection and handling mechanisms

## [1.2.0] - 2023-12-01

### Added
- TruffleHog integration for enhanced secret scanning
- Discord webhook notifications
- Basic pattern-based vulnerability detection
- Configurable diff context and noise reduction

### Changed
- Improved configuration management
- Enhanced logging with structured output
- Better error handling and recovery

### Fixed
- State file corruption issues
- Memory usage optimization
- Network timeout handling

## [1.1.0] - 2023-11-15

### Added
- Link extraction and categorization
- Basic vulnerability detection
- Telegram notification support
- State pruning and cleanup

### Changed
- Refactored scanning engine
- Improved notification formatting
- Enhanced configuration validation

### Fixed
- URL normalization issues
- Concurrent access problems
- Notification delivery reliability

## [1.0.0] - 2023-11-01

### Added
- Initial release of JSMon-NG
- JavaScript file monitoring and change detection
- Basic secret scanning capabilities
- State management with JSON persistence
- Simple notification system
- Configuration via YAML files
- Command-line interface

### Features
- SHA256-based change detection
- Recon workflow integration
- Basic diff generation
- File storage and organization
- Configurable monitoring intervals

---

## Release Notes

### Version 1.3.0 Highlights

This major release represents a complete architectural overhaul of JSMon-NG, focusing on:

1. **Performance**: Fully asynchronous design for handling hundreds of URLs efficiently
2. **Reliability**: Enhanced error handling, circuit breakers, and state management
3. **Scalability**: Improved memory management and concurrent processing
4. **Usability**: Better CLI interface, comprehensive configuration, and rich notifications
5. **Security**: Enhanced secret detection, vulnerability scanning, and input validation

### Migration Guide

When upgrading from v1.2.x to v1.3.0:

1. **Configuration**: Update your `config.yaml` file to use the new format
2. **Python Version**: Ensure you're using Python 3.9 or higher
3. **Dependencies**: Run `pip install --upgrade jsmon-ng[all]` to get all features
4. **State File**: The tool will automatically migrate your existing state file
5. **CLI**: Update any scripts to use the new CLI interface

### Breaking Changes

- Minimum Python version: 3.9+
- Configuration format changes
- CLI interface updates
- Some API changes for programmatic usage

For detailed migration instructions, see the [Migration Guide](docs/migration.md).

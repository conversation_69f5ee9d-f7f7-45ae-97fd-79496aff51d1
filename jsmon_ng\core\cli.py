"""JSMon-NG CLI module.

This module provides the command-line interface for JSMon-NG, including
the main monitoring loop and summary report generation.
"""

import asyncio
import click
import logging
import signal
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Optional, Set, Tuple, Any
from urllib.parse import urlparse, urlunparse
import aiofiles

from jsmon_ng.config.config import AppConfig
from jsmon_ng.state.types import (
    FindingType, HistoryEntry,
    UrlState, SummaryStats, FullState, History
)
from jsmon_ng.utils.logging_config import configure_logging
from jsmon_ng.config.cli_config import config_option, get_config, log_level_option
from jsmon_ng.network.async_fetcher import AsyncFetcher
from jsmon_ng.config.severity import load_weights_from_config

from jsmon_ng.analysis.scanner import Scanner
from pydantic import HttpUrl, ValidationError
from jsmon_ng.notifications.manager import (
    init_handlers as init_notification_handlers,
    notify_all as send_all_notifications,
    shutdown as shutdown_notification_handlers
)

JS_FILE_EXTENSION = '.js'

logger = logging.getLogger(__name__)

shutdown_event = asyncio.Event()

def _handle_signal(sig: Any, _: Any) -> None:
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {sig}, initiating graceful shutdown...")
    shutdown_event.set()

def _ensure_url_scheme(url_str: str, default_scheme: str = "https") -> str:
    parsed = urlparse(url_str)
    if not parsed.scheme:
        if parsed.netloc: 
            return urlunparse((default_scheme, parsed.netloc, parsed.path, parsed.params, parsed.query, parsed.fragment))
        else: 
            logger.warning(f"URL '{url_str}' lacks a scheme and netloc, cannot reliably normalize.")
            return url_str 
    return url_str

async def _load_target_urls(config: AppConfig) -> Tuple[Set[HttpUrl], int]:
    valid_http_urls: Set[HttpUrl] = set()
    raw_urls_processed = 0
    sources_processed = 0

    async def process_file(file_path: Path, url_set: Set[HttpUrl]) -> None:
        nonlocal raw_urls_processed
        if not file_path.is_file():
            logger.warning(f"JS URL list not found at {file_path}")
            return
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                async for line in f:
                    raw_urls_processed += 1
                    url_str = line.strip()
                    if not (url_str and url_str.endswith(JS_FILE_EXTENSION)):
                        continue
                    normalized_url_str = _ensure_url_scheme(url_str)
                    try:
                        http_url = HttpUrl(normalized_url_str)
                        url_set.add(http_url)
                    except ValidationError:
                        logger.warning(f"Invalid URL format skipped: '{url_str}' (normalized to '{normalized_url_str}') from {file_path}")
        except Exception as e:
            logger.error(f"Error reading JS URL list {file_path}: {e}", exc_info=True)

    if config.recon_base_directory and config.monitored_targets:
        logger.info(f"Using recon_base_directory: {config.recon_base_directory}")
        base_dir = Path(config.recon_base_directory)
        if not base_dir.is_dir():
            logger.error(f"Recon base directory does not exist or is not a directory: {base_dir}")
            return valid_http_urls, sources_processed
            
        for target_name in config.monitored_targets:
            path_to_js_list = base_dir / target_name / "javascript" / "alive_js_files.txt"
            sources_processed += 1
            await process_file(path_to_js_list, valid_http_urls)
    elif config.target_files:
        logger.info("Using legacy target_files configuration.")
        for target_file_path_str in config.target_files:
            target_file_path = Path(target_file_path_str)
            sources_processed += 1
            await process_file(target_file_path, valid_http_urls)
    else:
        logger.warning("No target sources configured.")

    logger.info(f"Loaded {len(valid_http_urls)} unique and valid JS URLs from {sources_processed} sources (processed {raw_urls_processed} raw lines).")
    if not valid_http_urls:
        logger.error("No valid URLs to monitor after processing target sources.")
    return valid_http_urls, sources_processed





async def _send_notification(
    config: AppConfig,
    title: str,
    message: str,
    url: Optional[str] = None,
    old_entry: Optional[HistoryEntry] = None,
    new_entry: Optional[HistoryEntry] = None,
    event_type: Optional[str] = None
) -> None:
    """Helper function to send notifications with consistent error handling.
    
    Args:
        config: The application configuration
        title: Notification title
        message: Notification message
        url: Optional URL associated with the notification
        old_entry: Optional previous history entry for diff notifications
        new_entry: Optional new history entry for diff notifications
        event_type: Type of event, determines notification rules:
            - "fetch": Uses notify_on_fetch_error
            - "processing": Uses notify_on_processing_error
            - "empty_content": Uses notify_on_fetch_error
            - "new_js": Uses notify_on_new_content
            - "content_change": Uses notify_on_changed_content
            - "new_findings": Uses notify_on_new_findings
            If None, notification is sent unconditionally
    """
    try:
        # Determine if we should send the notification based on event type
        should_notify = True
        if event_type:
            if event_type == "fetch":
                should_notify = config.notifications.notify_on_fetch_error
            elif event_type == "processing":
                should_notify = config.notifications.notify_on_processing_error
            elif event_type == "empty_content":
                should_notify = config.notifications.notify_on_fetch_error  # Using same flag as fetch errors
            elif event_type == "new_js":
                should_notify = config.notifications.notify_on_new_content
            elif event_type == "content_change":
                should_notify = config.notifications.notify_on_changed_content
            elif event_type == "new_findings":
                should_notify = config.notifications.notify_on_new_findings
        
        if should_notify:
            await send_all_notifications(
                cfg=config,
                title=title,
                message=message,
                url=url,
                old_entry=old_entry,
                new_entry=new_entry
            )
    except Exception as notify_error:
        logger.error(f"Failed to send notification: {notify_error}", exc_info=True)

def _load_state_safely(state_file_path: Path, keep_versions: Optional[int] = None) -> FullState:
    """Load state from file with error handling.

    Args:
        state_file_path: Path to the state file
        keep_versions: Number of versions to keep when loading

    Returns:
        Loaded FullState or empty FullState if loading fails
    """
    if state_file_path.exists():
        try:
            return FullState.load(state_file_path, keep_versions=keep_versions)
        except Exception as e:
            logger.error(f"Error loading state file {state_file_path}: {e}. Starting with empty state.", exc_info=True)
    return FullState()

async def _process_single_target_url(
    url_to_process_str: str,
    config: AppConfig,
    state: FullState,
    fetcher: AsyncFetcher,
    scanner: Scanner,
    shutdown_event: asyncio.Event
) -> Optional[str]:
    """Process a single target URL and return the URL if it was successfully processed."""
    if shutdown_event.is_set():
        return None

    try:
        prev_state_for_url = state.urls.get(url_to_process_str)
        logger.debug(f"Processing URL: {url_to_process_str} (Previous state exists: {prev_state_for_url is not None})")

        fetch_result = await fetcher.fetch(
            url_to_process_str,
            previous_etag=prev_state_for_url.etag if prev_state_for_url else None,
            previous_last_modified=prev_state_for_url.last_modified if prev_state_for_url else None
        )

        if not fetch_result.success:
            logger.warning(f"Failed to fetch {url_to_process_str}: {fetch_result.error}")
            await _send_notification(
                config=config,
                title=f"❌ Fetch Failed: {url_to_process_str}",
                message=f"Failed to fetch {url_to_process_str}:\nError: {fetch_result.error}",
                url=url_to_process_str,
                event_type="fetch"
            )
            return None

        if fetch_result.status == 304:
            logger.debug(f"Content unchanged for {url_to_process_str} (304 Not Modified)")
            return None

        content = fetch_result.text
        if not content:
            logger.warning(f"Empty content received for {url_to_process_str}")
            await _send_notification(
                config=config,
                title=f"⚠️ Empty Content: {url_to_process_str}",
                message=f"Received empty content from {url_to_process_str}",
                url=url_to_process_str,
                event_type="empty_content"
            )
            return None

        # Process content and update state
        content_findings = await scanner.scan(content, url_to_process_str, state)

        # Save file and run TruffleHog
        file_path = Path(config.download_directory) / f"{fetch_result.sha256}.js"
        file_path.parent.mkdir(parents=True, exist_ok=True)

        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(content)

        trufflehog_findings = await scanner.scan_file_with_trufflehog(file_path, url_to_process_str, state)
        all_findings = content_findings + trufflehog_findings

        # Create new history entry
        new_history_entry = HistoryEntry(
            timestamp=datetime.now(timezone.utc),
            findings=all_findings,
            risk_score=min(sum(f.severity for f in all_findings), 100),
            hash=fetch_result.sha256,
            size=fetch_result.size if fetch_result.size is not None else len(content.encode('utf-8')),
            filename=file_path.name
        )

        # Create or update URL state
        if prev_state_for_url:
            history_instance = prev_state_for_url.history
        else:
            history_instance = History()

        history_instance.add_entry(new_history_entry)

        # Create new state entry
        try:
            new_url_state = UrlState(
                url=url_to_process_str,
                current_hash=fetch_result.sha256,
                etag=fetch_result.headers.get('etag'),
                last_modified=fetch_result.headers.get('last-modified'),
                current_size=fetch_result.size if fetch_result.size is not None else len(content.encode('utf-8')),
                current_filename=file_path.name,
                current_risk_score=min(sum(f.severity for f in all_findings), 100),
                current_findings=all_findings,
                history=history_instance
            )
            logger.debug(f"Successfully created new UrlState for {url_to_process_str}")
        except ValidationError as ve:
            logger.error(f"Validation error creating UrlState for {url_to_process_str}: {ve}", exc_info=True)
            raise

        # Update state
        state.urls[url_to_process_str] = new_url_state

        # Check if this is a new URL or content changed
        is_new_url = prev_state_for_url is None
        content_changed = prev_state_for_url and prev_state_for_url.current_hash != fetch_result.sha256

        # Send notifications
        if is_new_url:
            await _send_notification(
                config=config,
                title=f"🆕 New JavaScript File: {url_to_process_str}",
                message=f"New JavaScript file discovered:\n{url_to_process_str}\nSize: {new_url_state.current_size:,} bytes\nFindings: {len(all_findings)}",
                url=url_to_process_str,
                event_type="new_js"
            )
        elif content_changed:
            await _send_notification(
                config=config,
                title=f"🔄 JavaScript File Changed: {url_to_process_str}",
                message=f"JavaScript file content changed:\n{url_to_process_str}\nNew size: {new_url_state.current_size:,} bytes\nFindings: {len(all_findings)}",
                url=url_to_process_str,
                event_type="content_change"
            )

        # Send findings notifications
        if all_findings:
            for finding in all_findings:
                await _send_notification(
                    config=config,
                    title=f"🔍 {finding.type.value.title()} Found: {url_to_process_str}",
                    message=f"Finding: {finding.reason}\nLine: {finding.line}\nSeverity: {finding.severity}/10\nPreview: {finding.preview[:200]}",
                    url=url_to_process_str,
                    event_type="new_findings"
                )

        return url_to_process_str

    except Exception as e:
        logger.error(f"Error processing {url_to_process_str}: {e}", exc_info=True)
        await _send_notification(
            config=config,
            title=f"❌ Error Processing: {url_to_process_str}",
            message=f"Error occurred while processing {url_to_process_str}:\n{str(e)}",
            url=url_to_process_str,
            event_type="processing"
        )
        return None

async def _run_monitor_cycle(
    config: AppConfig,
    state: FullState,
    fetcher: AsyncFetcher,
    scanner: Scanner,
    target_http_urls_set: Set[str],
    shutdown_event: asyncio.Event
) -> None:
    """Run a single monitoring cycle."""
    try:
        processed_urls_in_cycle = 0

        # Create tasks for all URLs
        tasks = [
            _process_single_target_url(
                url, config, state, fetcher, scanner, shutdown_event
            )
            for url in target_http_urls_set
        ]

        # Process URLs concurrently
        for future in asyncio.as_completed(tasks):
            if shutdown_event.is_set():
                logger.info("Shutdown event received during URL processing, cancelling remaining tasks.")
                break

            try:
                processed_url = await future
                if processed_url:
                    processed_urls_in_cycle += 1
            except Exception as e:
                logger.error(f"Unhandled error from _process_single_target_url future: {e}")

        logger.info(f"Monitoring cycle completed. Processed {processed_urls_in_cycle} URLs.")

    except Exception as e:
        logger.error(f"Error in monitor cycle: {e}", exc_info=True)
        await _send_notification(
            config=config,
            title="❌ Monitor Cycle Error",
            message=f"An error occurred during the monitoring cycle:\n{str(e)}",
            event_type="processing"
        )

@click.group(invoke_without_command=True)
@click.pass_context
def cli(ctx: click.Context):
    if ctx.invoked_subcommand is None:
        click.echo(cli.get_help(ctx))

@cli.command()
@config_option
@log_level_option
def run(config_file: str, log_level: Optional[str]):
    config = get_config(config_file)
    configure_logging(level=log_level, log_settings_model=config.logging, use_rich=None)
    
    download_dir = Path(config.download_directory)
    download_dir.mkdir(parents=True, exist_ok=True)

    loop = asyncio.get_event_loop()
    # Only add signal handlers on Unix-like systems
    if sys.platform != 'win32':
        for sig in (signal.SIGINT, signal.SIGTERM):
            loop.add_signal_handler(sig, _handle_signal, sig, None)
    else:
        # On Windows, we rely on KeyboardInterrupt handling
        logger.debug("Running on Windows, using KeyboardInterrupt handling instead of signal handlers")

    current_full_state_outer: Optional[FullState] = None 

    async def _main_loop_inner(fetcher: AsyncFetcher, scanner: Scanner) -> None:
        nonlocal current_full_state_outer 
        while not shutdown_event.is_set():
            cycle_start_time_log = datetime.now(timezone.utc)
            logger.info(f"Starting monitoring cycle at {cycle_start_time_log.isoformat()}")
            
            if current_full_state_outer is None: 
                 current_full_state_outer = FullState()

            try:
                # Load target URLs from configuration
                target_urls_from_config, sources_processed = await _load_target_urls(config)
                if not target_urls_from_config:
                    logger.warning("No target URLs loaded from configuration. Skipping cycle.")
                    continue

                # Convert HttpUrl objects to strings for _run_monitor_cycle
                target_url_strings_set = {str(url) for url in target_urls_from_config}
                logger.info(f"Loaded {len(target_url_strings_set)} target URLs from {sources_processed} sources")

                # Run monitoring cycle with loaded URLs
                await _run_monitor_cycle(
                    config, 
                    current_full_state_outer, 
                    fetcher, 
                    scanner, 
                    target_url_strings_set, 
                    shutdown_event
                )
                
                end_time_log = datetime.now(timezone.utc).isoformat()
                logger.info(f"Cycle ended at {end_time_log}")
            except Exception as e_cycle: 
                logger.error(f"Error within monitoring cycle: {e_cycle}", exc_info=True)
                if current_full_state_outer is None: 
                    current_full_state_outer = FullState()
            
            if shutdown_event.is_set():
                logger.info("Shutdown signaled during cycle, exiting loop.")
                break
            
            try:
                logger.info(f"Sleeping for {config.run_interval_seconds} seconds until next cycle.")
                await asyncio.wait_for(shutdown_event.wait(), timeout=config.run_interval_seconds)
                if shutdown_event.is_set():
                    logger.info("Shutdown signaled during sleep, exiting loop.")
                    break
            except asyncio.TimeoutError:
                pass 
            except asyncio.CancelledError:
                logger.info("Sleep cancelled, likely due to shutdown.")
                break

    async def _main_runner() -> None:
        nonlocal current_full_state_outer
        
        state_file_path = Path(config.state_file)
        if current_full_state_outer is None:
            current_full_state_outer = _load_state_safely(state_file_path, config.cleanup.keep_versions)
            logger.info(f"Initial state loaded for main_runner from {config.state_file} with {len(current_full_state_outer.urls)} URLs.")

        # Initialize notification handlers
        try:
            init_notification_handlers(config)
            logger.info("Notification handlers initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize notification handlers: {e}", exc_info=True)

        async with AsyncFetcher(config) as fetcher:
            scanner = Scanner(config, current_full_state_outer)
            try:
                await _main_loop_inner(fetcher, scanner)
            except asyncio.CancelledError:
                logger.info("Main loop task explicitly cancelled.")
            finally:
                logger.info("Main loop finished or was cancelled. Performing final cleanup.")
                if not shutdown_event.is_set():
                    shutdown_event.set()

                # Cleanup hanging TruffleHog processes
                try:
                    logger.info("Cleaning up any hanging TruffleHog processes...")
                    await scanner.cleanup_hanging_processes()
                    logger.info("TruffleHog process cleanup completed")
                except Exception as e:
                    logger.error(f"Error cleaning up TruffleHog processes: {e}", exc_info=True)

                # Shutdown notification handlers
                try:
                    logger.info("Shutting down notification handlers...")
                    await shutdown_notification_handlers()
                    logger.info("Notification handlers shut down successfully")
                except Exception as e:
                    logger.error(f"Error shutting down notification handlers: {e}", exc_info=True)

                if current_full_state_outer and hasattr(current_full_state_outer, 'save') and Path(config.state_file).parent.exists():
                    try:
                        logger.info("Attempting to save final state on exit...")
                        current_full_state_outer.save(Path(config.state_file))
                        logger.info("Final state saved.")
                    except Exception as e_save:
                        logger.error(f"Could not save final state: {e_save}", exc_info=True)

    main_task = None
    try:
        state_file_path_init = Path(config.state_file)
        if not current_full_state_outer:  # Load only if not already loaded by _main_runner or init
            current_full_state_outer = _load_state_safely(state_file_path_init, config.cleanup.keep_versions)
        
        main_task = loop.create_task(_main_runner(), name="main_runner_task")
        loop.run_until_complete(main_task)
    except KeyboardInterrupt: 
        logger.info("KeyboardInterrupt received. Setting shutdown event.")
        shutdown_event.set()
        if main_task and not main_task.done(): 
            logger.info("Attempting to await main_task after KeyboardInterrupt...")
            try:
                loop.run_until_complete(asyncio.wait_for(main_task, timeout=10.0)) 
            except asyncio.TimeoutError:
                logger.warning("Main task did not finish cleanly within timeout after KeyboardInterrupt.")
            except asyncio.CancelledError:
                logger.info("Main task was cancelled successfully after KeyboardInterrupt.")
            except Exception as e_gb: 
                logger.error(f"Error during main_task cleanup on KeyboardInterrupt: {e_gb}", exc_info=True)

    except Exception as e:
        logger.critical(f"Fatal error running main_runner: {e}", exc_info=True)
        if not shutdown_event.is_set() and current_full_state_outer and hasattr(current_full_state_outer, 'save'):
            try:
                logger.error("Attempting emergency state save due to critical error...")
                current_full_state_outer.save(Path(config.state_file))
                logger.info("Emergency state save successful.")
            except Exception as e_save:
                logger.error(f"Emergency state save failed: {e_save}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("Application shutting down (final block).")
        
        tasks_to_cancel_final = [t for t in asyncio.all_tasks(loop) if t is not asyncio.current_task(loop)]
        if tasks_to_cancel_final:
            logger.info(f"Cancelling {len(tasks_to_cancel_final)} outstanding tasks...")
            for task_in_final_cancel in tasks_to_cancel_final: 
                task_in_final_cancel.cancel()
            try:
                loop.run_until_complete(asyncio.gather(*tasks_to_cancel_final, return_exceptions=True))
                logger.info("Outstanding tasks gathered/cancelled.")
            except RuntimeError as e_rt: 
                logger.warning(f"RuntimeError while gathering cancelled tasks (loop might be closed): {e_rt}")
            except Exception as e_gather:
                logger.error(f"Exception while gathering cancelled tasks: {e_gather}", exc_info=True)

        # Only remove signal handlers on Unix-like systems
        if sys.platform != 'win32':
            for sig in (signal.SIGINT, signal.SIGTERM):
                try:
                    loop.remove_signal_handler(sig)
                except Exception as e_signal:
                    logger.debug(f"Error removing signal handler for {sig}: {e_signal}")
        
        logger.info("Shutdown sequence complete.")

@cli.command()
@config_option
@log_level_option
def summary(config_file: str, log_level: Optional[str]):
    """Generate a summary report."""
    try:
        config = get_config(config_file)
        configure_logging(level=log_level, log_settings_model=config.logging, use_rich=None)
        if config.severity: 
            load_weights_from_config(config)
        
        logger.info("Generating summary report")
        
        state_file_path = Path(config.state_file)
        if not state_file_path.exists():
            logger.error(f"State file not found: {state_file_path}")
            sys.exit(1)
            
        try:
            full_state = FullState.load(state_file_path)
        except Exception as e:
            logger.error(f"Error loading state file for summary: {e}", exc_info=True)
            sys.exit(1)
            
        stats = SummaryStats()
        for _, url_data in full_state.urls.items():
            stats.total_urls += 1
            if url_data.current_errors or url_data.consecutive_404s > 0:
                stats.error_urls += 1
                
            findings = url_data.current_findings
            if findings:
                stats.secrets_count += sum(1 for f in findings if f.type == FindingType.SECRET)
                stats.patterns_count += sum(1 for f in findings if f.type == FindingType.PATTERN)
                stats.endpoints_count += sum(1 for f in findings if f.type == FindingType.ENDPOINT)
                stats.links_count += sum(1 for f in findings if f.type == FindingType.LINK)
                stats.vulnerabilities_count += sum(1 for f in findings if f.type == FindingType.VULNERABILITY)
            
        logger.info("--- Summary Report ---")
        logger.info(f"Timestamp: {datetime.now(timezone.utc).isoformat()}")
        logger.info(f"Monitored URLs: {stats.total_urls}")
        logger.info(f"URLs with Current Errors: {stats.error_urls}")
        logger.info(f"Total Findings Across All URLs:")
        logger.info(f"  - Secrets: {stats.secrets_count}")
        logger.info(f"  - Patterns: {stats.patterns_count}")
        logger.info(f"  - Endpoints: {stats.endpoints_count}")
        logger.info(f"  - Links: {stats.links_count}")
        logger.info(f"  - Vulnerabilities: {stats.vulnerabilities_count}")
        
        if full_state.last_cycle:
            lc = full_state.last_cycle
            start_iso = lc.start_time.isoformat() if lc.start_time else "N/A"
            end_iso = lc.end_time.isoformat() if lc.end_time else "N/A"
            logger.info(f"Last Cycle ({start_iso} to {end_iso}):")
            logger.info(f"  - Duration: {lc.duration_seconds:.2f}s")
            logger.info(f"  - Checked: {lc.urls_checked}, Changed: {lc.urls_changed}, New: {lc.urls_new}, Errors: {lc.urls_errors}")
            logger.info(f"  - Cycle Findings: Secrets: {lc.secrets_found}, Vulns: {lc.vulnerabilities_found}, Links: {lc.links_found}")
        logger.info("--- End of Report ---")
        
    except Exception as e:
        logger.error(f"Error generating summary: {e}", exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    cli()
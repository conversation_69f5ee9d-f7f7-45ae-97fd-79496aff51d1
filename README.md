# 🚀 JSMon-NG

<div align="center">

[![Python Version](https://img.shields.io/badge/python-3.9%2B-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/sl4x0/jsmon-ng/pulls)
[![Twitter](https://img.shields.io/twitter/follow/sl4x0?style=social)](https://twitter.com/sl4x0)

A powerful JavaScript monitoring tool for continuous security analysis and change detection 🔍

[Features](#features) • [Installation](#installation) • [Configuration](#configuration) • [Usage](#usage) • [Contributing](#contributing)

</div>

---

## 📋 Overview

JSMon-NG is a robust tool designed to monitor JavaScript files for changes and security issues. It integrates with reconnaissance workflows and provides comprehensive analysis capabilities for JavaScript files across your target domains.

## ✨ Features

### 🎯 Core Functionality

- **🔄 Continuous JavaScript Monitoring**  
  Monitors specified JavaScript files or all JS files discovered in target directories for changes and potential security issues.

- **🔍 Recon Workflow Integration**  
  Designed to integrate with reconnaissance workflows by reading lists of live JavaScript URLs from a structured reconnaissance directory.

- **📊 Change Detection**  
  Tracks changes in JS files based on their SHA256 content hash.

- **💾 Content Storage**  
  Downloads and stores copies of monitored JavaScript files, named by their content hash for deduplication.

- **📝 State Management**
  - Maintains a persistent JSON state file (`jsmon_state.json`)
  - Includes history tracking with configurable pruning
  - Features atomic writes for state file integrity
  - Tracks "seen" findings for each URL

### 🔎 Scanning & Analysis

- **🔐 Secret Scanning**

  - Integrates with TruffleHog CLI
  - Detects API keys, tokens, and credentials

- **🔗 Link Extraction**

  - Extracts URLs using configurable regex patterns
  - Normalizes and categorizes links
  - Tracks and notifies on newly discovered links

- **⚠️ Vulnerability Detection**

  - Prototype Pollution detection
  - Template Injection analysis
  - Configurable list of active vulnerability detectors

- **🔍 Pattern-Based Scanning**
  - Generic API keys and tokens
  - Cloud provider keys
  - Service-specific tokens
  - Common password patterns
  - Dangerous JavaScript functions

### 🔄 Diffing & Change Analysis

- **📝 Unified Diffs**  
  Generates unified diffs for content changes

- **🎨 Semantic Diffing**  
  Optional Prettier integration for cleaner diffs

- **⚡ Native Diff Utility**  
  System diff command support for large files

- **🔍 Diff Noise Reduction**
  - Whitespace change filtering
  - Configurable minimum change threshold
  - Regex-based line filtering

### 🌐 Networking & Fetching

- **⚡ Asynchronous Operations**
  Fully async/await based architecture for maximum performance

- **🔧 Advanced HTTP Client**
  HTTP/2 support, connection pooling, and detailed configuration

- **🔄 Conditional GETs**
  ETag and Last-Modified support for efficient bandwidth usage

- **🔌 Circuit Breaker**
  Per-host circuit breaker implementation with automatic recovery

- **🎯 Concurrency Control**
  Domain-based request limiting and connection management

- **🔄 Retry Logic**
  Configurable retry mechanisms with exponential backoff

- **⏱️ Timeout Management**
  Granular timeout controls for different operations

### 📢 Notifications

- **📨 Multiple Channels**

  - Discord webhooks
  - Telegram bots

- **⚙️ Configurable Alerts**

  - File Changes
  - Secrets Found
  - Pattern Matches

- **🎨 Rich Formatting**
  - Markdown support
  - Customizable colors
  - Enhanced formatting options

## 🚀 Installation

### Option 1: Install from PyPI (Recommended)

```bash
# Install JSMon-NG
pip install jsmon-ng

# Install optional dependencies for enhanced performance
pip install jsmon-ng[performance]

# Install all optional dependencies
pip install jsmon-ng[all]
```

### Option 2: Install from Source

```bash
# Clone the repository
git clone https://github.com/sl4x0/jsmon-ng.git
cd jsmon-ng

# Install in development mode
pip install -e .

# Or install with optional dependencies
pip install -e .[all]
```

### External Dependencies

```bash
# Install TruffleHog (optional, for enhanced secret scanning)
pip install trufflehog

# Install Prettier (optional, for better diff formatting)
npm install -g prettier
```

## ⚙️ Configuration

Create a `config.yaml` file in your project directory:

```yaml
# Target Configuration
recon_base_directory: "/path/to/recon"
monitored_targets:
  - "example.com"
  - "test.com"

# Core Settings
download_directory: "./downloads"
state_file: "./jsmon_state.json"
run_interval_seconds: 300

# Scanner Settings
scanner:
  link_extraction:
    enabled: true
    patterns:
      - "https?://[^\\s\"'<>]+"
    exclude_patterns:
      - "data:image/[^\\s\"'<>]+"
  vulnerability_detection:
    enabled: true
    detectors:
      - "prototype_pollution"
      - "template_injection"

# Notifications
notifications:
  discord:
    enabled: true
    webhook_url: "your_webhook_url"
```

## 💻 Usage

### Basic Usage

```bash
# Start monitoring with default configuration
jsmon-ng run

# Start monitoring with custom configuration
jsmon-ng run --config custom_config.yaml

# Generate summary report
jsmon-ng summary

# Show help
jsmon-ng --help
```

### Command Line Options

```bash
# Run with specific configuration and log level
jsmon-ng run --config custom_config.yaml --log-level INFO

# Generate summary with custom output
jsmon-ng summary --output report.json

# Run in verbose mode
jsmon-ng run --verbose

# Run with custom state file
jsmon-ng run --state-file custom_state.json
```

### Advanced Usage

```bash
# Monitor specific URLs from command line
jsmon-ng run --urls "https://example.com/app.js,https://test.com/main.js"

# Run with custom download directory
jsmon-ng run --download-dir ./custom_downloads

# Run with specific interval
jsmon-ng run --interval 600  # 10 minutes
```

## 🏗️ Architecture & Performance

### Asynchronous Design

JSMon-NG is built with a fully asynchronous architecture:

- **Concurrent URL Processing**: Multiple URLs are processed simultaneously
- **Non-blocking I/O**: Network operations don't block other tasks
- **Efficient Resource Usage**: Optimal CPU and memory utilization
- **Scalable Design**: Handles hundreds of URLs efficiently

### Performance Optimizations

- **Connection Pooling**: Reuses HTTP connections for better performance
- **HTTP/2 Support**: Leverages modern HTTP protocol features
- **Conditional Requests**: Minimizes bandwidth usage with ETag/Last-Modified
- **Native Diff**: Uses system diff command for large files
- **Async File I/O**: Non-blocking file operations

### Memory Management

- **Streaming Processing**: Large files are processed in chunks
- **State Pruning**: Automatic cleanup of old state data
- **Efficient Caching**: Smart caching strategies for better performance

## 🛡️ Error Handling & Robustness

- **🔄 Graceful Exit**  
  Handles Ctrl+C for clean shutdown

- **🧹 State Pruning**  
  Configurable URL pruning based on consecutive 404s

- **📊 Error Tracking**  
  Per-URL fetch error tracking

- **🔌 Circuit Breaker**  
  Prevents overwhelming unresponsive servers

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

<div align="center">

Made with ❤️ by the JSMon-NG Team

</div>

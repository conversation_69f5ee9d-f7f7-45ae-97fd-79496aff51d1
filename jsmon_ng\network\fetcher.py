"""Synchronous HTTP client for JSMon-NG.

This module provides a synchronous HTTP client for fetching JavaScript files
with features like:
- Automatic retries (simplified, primary retry logic in FetcherConfig)
- Circuit breaking
- Rate limiting (via httpx transport if configured)
- Timeout handling
"""

import logging
import time
from typing import Optional, Dict
from urllib.parse import urlparse
import httpx

from ..config.config import AppConfig, FetcherConfig
from .circuit_breaker import CircuitBreaker
from .common import FetchResult

log = logging.getLogger(__name__)

class Client:
    """Synchronous HTTP client with configuration."""

    def __init__(self, cfg: FetcherConfig):
        """Initialize client with configuration.
        
        Args:
            cfg: Fetcher configuration (subset of AppConfig)
        """
        self.cfg = cfg
        
        # Configure timeouts from FetcherConfig
        timeout = httpx.Timeout(
            connect=cfg.connect_timeout,
            read=cfg.read_timeout,
            write=cfg.read_timeout,  # Often same as read
            pool=cfg.timeout  # Overall pool/total timeout
        )

        # Configure connection limits from FetcherConfig
        limits = httpx.Limits(
            max_connections=cfg.max_connections,
            max_keepalive_connections=cfg.max_keepalive
        )
        
        self.client = httpx.Client(
            timeout=timeout,
            limits=limits,
            http2=cfg.http2_enabled,
            verify=cfg.verify_ssl,
            trust_env=cfg.trust_env,
            follow_redirects=True,  # httpx default is False for Client
            max_redirects=cfg.max_redirects,
            headers=cfg.headers.copy()  # Use a copy
        )
        log.debug(f"Initialized sync Client with timeout: {timeout}, limits: {limits}")
        
    def get(self, url: str, headers: Optional[Dict[str, str]] = None) -> httpx.Response:
        """Makes a GET request."""
        request_headers = self.client.headers.copy()
        if headers:
            request_headers.update(headers)
        return self.client.get(url, headers=request_headers)

    def head(self, url: str, headers: Optional[Dict[str, str]] = None) -> httpx.Response:
        """Makes a HEAD request."""
        request_headers = self.client.headers.copy()
        if headers:
            request_headers.update(headers)
        return self.client.head(url, headers=request_headers)

    def close(self) -> None:
        """Close the HTTP client."""
        if hasattr(self, 'client') and self.client:
            self.client.close()
        log.debug("Closed sync Client")


class Fetcher:
    """Synchronous URL fetcher with circuit breaking and retries."""

    def __init__(self, app_config: AppConfig):
        """Initialize fetcher with configuration.
        
        Args:
            app_config: Application configuration
        """
        self.app_config = app_config
        self.fetch_cfg: FetcherConfig = app_config.fetcher  # Specific fetcher settings
        self.network_cfg = app_config.network  # For circuit breaker settings

        self.http_client = Client(self.fetch_cfg)  # Pass FetcherConfig to sync Client

        self.circuit_breaker = CircuitBreaker(  # Uses settings from NetworkConfig.CircuitBreakerConfig
            failure_threshold=self.network_cfg.circuit_breaker.failure_threshold,
            recovery_timeout=self.network_cfg.circuit_breaker.recovery_timeout,
            half_open_timeout=self.network_cfg.circuit_breaker.half_open_timeout
        )
        log.debug("Initialized sync Fetcher")
        
    def __enter__(self) -> 'Fetcher':
        """Enter context."""
        return self
        
    def __exit__(self, *_) -> None:
        """Exit context."""
        self.http_client.close()
        log.debug("Sync Fetcher exited context and closed client.")
        
    def _get_host(self, url: str) -> str:
        """Extract host from URL."""
        return urlparse(url).netloc
        
    def fetch(self, url: str, previous_etag: Optional[str] = None, previous_last_modified: Optional[str] = None) -> FetchResult:
        """Fetch content from URL with circuit breaking and retries.
        
        Args:
            url: URL to fetch
            previous_etag: ETag from previous fetch for conditional GET
            previous_last_modified: Last-Modified header from previous fetch
            
        Returns:
            FetchResult object
        """
        host = self._get_host(url)
        start_time = time.time()

        if not self.circuit_breaker.allow_request(host):
            log.warning(f"Circuit breaker open for {host}, skipping request to {url}")
            return FetchResult(
                text=None, sha256=None, size=None, headers=None, status=0,
                success=False, error={"type": "circuit_breaker", "message": f"Circuit open for {host}"},
                duration=time.time() - start_time
            )
            
        conditional_headers = {}
        if previous_etag:
            conditional_headers['If-None-Match'] = previous_etag
        if previous_last_modified:
            conditional_headers['If-Modified-Since'] = previous_last_modified

        for attempt in range(1, self.fetch_cfg.retries + 1):  # Use FetcherConfig.retries
            try:
                log.debug(f"Sync fetch attempt {attempt}/{self.fetch_cfg.retries} for {url}")
                response = self.http_client.get(url, headers=conditional_headers)

                if response.status_code == 304:  # Not Modified
                    self.circuit_breaker.on_success(host)
                    log.info(f"Content not modified (304) for {url}")
                    return FetchResult(
                        text=None, sha256=None, size=None,
                        headers=dict(response.headers), status=304, success=True,
                        error=None, duration=time.time() - start_time
                    )

                response.raise_for_status()  # Raises HTTPStatusError for 4xx/5xx

                content_bytes = response.content
                text_content = content_bytes.decode('utf-8', errors='replace')

                self.circuit_breaker.on_success(host)
                log.debug(f"Successfully fetched (sync) {url} in {time.time() - start_time:.2f}s")
                return FetchResult(
                    text=text_content, sha256=None, size=len(content_bytes),  # sha256 can be computed later
                    headers=dict(response.headers), status=response.status_code, success=True,
                    error=None, duration=time.time() - start_time
                )
                
            except httpx.HTTPStatusError as e:
                self.circuit_breaker.on_failure(host, e)  # Pass exception
                log.warning(f"HTTP error {e.response.status_code} for {url} on attempt {attempt}: {e}")
                if attempt == self.fetch_cfg.retries:
                    log.error(f"Final attempt failed for {url} with HTTP error: {e}")
                    return FetchResult(
                        text=None, sha256=None, size=None,
                        headers=dict(e.response.headers) if hasattr(e, 'response') else None,
                        status=e.response.status_code if hasattr(e, 'response') else 0,
                        success=False,
                        error={"type": "http_error", "message": str(e), "status_code": e.response.status_code if hasattr(e, 'response') else 0},
                        duration=time.time() - start_time
                    )
                time.sleep(self.fetch_cfg.retry_delay * attempt)  # Exponential backoff basic
            
            except httpx.RequestError as e:  # Covers ConnectError, Timeout etc.
                self.circuit_breaker.on_failure(host, e)
                log.warning(f"Request error for {url} on attempt {attempt}: {e}")
                if attempt == self.fetch_cfg.retries:
                    log.error(f"Final attempt failed for {url} with request error: {e}")
                    return FetchResult(
                        text=None, sha256=None, size=None, headers=None, status=0,
                        success=False, error={"type": "request_error", "message": str(e)},
                        duration=time.time() - start_time
                    )
                time.sleep(self.fetch_cfg.retry_delay * attempt)

            except Exception as e:  # Catch-all for unexpected errors during fetch
                self.circuit_breaker.on_failure(host, e)  # Record generic failure
                log.error(f"Unexpected error fetching {url} on attempt {attempt}: {e}", exc_info=True)
                # For unexpected errors, probably don't retry unless specifically designed for it.
                return FetchResult(
                    text=None, sha256=None, size=None, headers=None, status=0,
                    success=False, error={"type": "unexpected_error", "message": str(e)},
                    duration=time.time() - start_time
                )
                
        # Should be unreachable if loop completes, as errors return FetchResult
        log.error(f"Sync fetch for {url} exhausted retries without returning a specific error FetchResult.")
        return FetchResult(
            text=None, sha256=None, size=None, headers=None, status=0,
            success=False, error={"type": "unknown_error", "message": "Exhausted retries"},
            duration=time.time() - start_time
        )

"""Telegram notification handler for JSMon-NG.

This module provides:
- Telegram bot integration
- Rich message formatting
- Error handling and retries
"""

import logging
from typing import Any, Dict, Optional

import aiohttp
from pydantic import BaseModel

from jsmon_ng.notifications.handler import NotificationHandler
from jsmon_ng.notifications.payloads import build_telegram_payload
from jsmon_ng.notifications.formatter import EnhancedNotificationFormatter
from jsmon_ng.state.types import HistoryEntry

logger = logging.getLogger(__name__)

class TelegramConfig(BaseModel):
    """Telegram notification configuration."""
    bot_token: str
    chat_id: str
    timeout: float = 30.0

class TelegramHandler(NotificationHandler):
    """Telegram notification handler."""
    def __init__(self, cfg: TelegramConfig, **backoff):
        super().__init__(**backoff)
        self.cfg = cfg
        self.formatter = EnhancedNotificationFormatter()

    async def send_notification(
        self,
        title: str,
        message: str,
        url: str,
        old_entry: Optional[HistoryEntry] = None,
        new_entry: Optional[HistoryEntry] = None,
        **kwargs: Any,
    ) -> bool:
        """Send a notification to Telegram.

        Args:
            title: Notification title
            message: Notification message
            url: URL that triggered the notification
            old_entry: Previous history entry (for diff notifications)
            new_entry: New history entry (for diff or new item notifications)
            **kwargs: Additional notification-specific arguments

        Returns:
            bool: True if notification was sent successfully
        """
        try:
            session = await self._get_session()

            # Format the message based on whether we have diff data
            if old_entry and new_entry:
                _, text = self.formatter.format_diff_notification(old_entry, new_entry, url)
            else:
                # For general messages, include title if it's different from message
                if title and title != message:
                    text = f"<b>{title}</b>\n\n{message}"
                else:
                    text = message

            payload = build_telegram_payload(
                chat_id=self.cfg.chat_id,
                text=text,
            )
            api_url = f"https://api.telegram.org/bot{self.cfg.bot_token}/sendMessage"

            await self._retry_with_backoff(self._post, session, api_url, payload.model_dump())
            logger.debug(f"Telegram notification sent for {url} with title '{title}'")
            return True

        except Exception as e:
            logger.error(f"[TelegramHandler] Failed to send notification for {url}: {e}", exc_info=True)
            return False

    async def _post(self, session, url: str, data: Dict[str, Any]) -> None:
        async with session.post(url, json=data, timeout=self.cfg.timeout) as resp:
            resp.raise_for_status() 
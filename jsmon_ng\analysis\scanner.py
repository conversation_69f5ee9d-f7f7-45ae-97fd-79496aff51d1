"""Scanner module for JSMon-NG.

This module provides a unified interface for scanning JavaScript content
using various detectors and pattern matching.
"""

import asyncio
import logging
import re
import json
import shutil
import subprocess
import hashlib
import threading
from typing import List, Dict, Any, Optional, Tuple, Set, Pattern
from pathlib import Path

from ..config.config import AppConfig
from ..state.types import Finding, FindingType, FindingList, FullState

# Import detectors
from .detectors.detectors.link_extractor import LinkExtractor
from .detectors.detectors.prototype_pollution_detector import PrototypePollutionDetector
from .detectors.detectors.template_injection_detector import TemplateInjectionDetector

log = logging.getLogger(__name__)

# Helper for severity mapping
SEVERITY_STRING_TO_INT_MAP = {
    "critical": 10,
    "high": 8,
    "medium": 6,
    "low": 3,
    "info": 1,
}

def _get_mapped_severity_int(severity_input: Optional[Any]) -> int:
    if isinstance(severity_input, int) and 0 <= severity_input <= 10:
        return severity_input
    if isinstance(severity_input, str):
        return SEVERITY_STRING_TO_INT_MAP.get(severity_input.lower(), 6)  # Default to medium (6)
    return 6  # Default for other types or None

class Scanner:
    """Unified scanner for JavaScript content analysis.
    
    This class coordinates multiple detectors and pattern matching to analyze
    JavaScript content for various types of findings.
    """
    
    def __init__(self, config: AppConfig, state: Optional[FullState] = None):
        """Initialize the scanner with configuration.
        
        Args:
            config: Application configuration
            state: Current application state for tracking findings
        """
        self.config = config
        self.scanner_config = config.scanner
        self.state = state # This is the overall FullState, used by scan method for context
        
        self.trufflehog_enabled = False
        self.trufflehog_command_path = None
        self.trufflehog_semaphore = None
        self._active_trufflehog_processes: Set[asyncio.subprocess.Process] = set()
        if self.scanner_config.trufflehog.enabled:
            self.trufflehog_command_path = shutil.which(self.scanner_config.trufflehog.command)
            if self.trufflehog_command_path:
                self.trufflehog_enabled = True
                self.trufflehog_semaphore = threading.Semaphore(self.scanner_config.trufflehog.max_concurrent_scans)
                log.info(f"TruffleHog integration enabled. Executable found at: {self.trufflehog_command_path}")
                log.info(f"TruffleHog concurrency set to {self.scanner_config.trufflehog.max_concurrent_scans} concurrent scans")
            else:
                log.warning(f"TruffleHog is enabled in config but command '{self.scanner_config.trufflehog.command}' not found in PATH. TruffleHog scanning will be skipped.")
                self.trufflehog_enabled = False
        
        self.detectors: Dict[str, Any] = {}
        
        if self.scanner_config.link_extraction.enabled:
            try:
                self.detectors['link_extractor'] = LinkExtractor(config)
                log.info("LinkExtractor initialized") # Changed from debug for visibility
            except Exception as e:
                log.error(f"Failed to initialize LinkExtractor: {e}", exc_info=True)
        
        if self.scanner_config.vulnerability_detection.enabled:
            if 'prototype_pollution' in self.scanner_config.vulnerability_detection.detectors:
                try:
                    self.detectors['prototype_pollution'] = PrototypePollutionDetector(config)
                    log.info("PrototypePollutionDetector initialized") # Changed from debug
                except Exception as e:
                    log.error(f"Failed to initialize PrototypePollutionDetector: {e}", exc_info=True)
            
            if 'template_injection' in self.scanner_config.vulnerability_detection.detectors:
                try:
                    self.detectors['template_injection'] = TemplateInjectionDetector(config)
                    log.info("TemplateInjectionDetector initialized") # Changed from debug
                except Exception as e:
                    log.error(f"Failed to initialize TemplateInjectionDetector: {e}", exc_info=True)
        
        # Initialize basic pattern scanning if enabled
        self.basic_pattern_definitions: List[Tuple[str, FindingType, int, float]] = []
        self.compiled_basic_patterns: List[Tuple[Pattern[str], FindingType, int, float]] = []
        
        if self.scanner_config.basic_pattern_scanning:
            self.basic_pattern_definitions = [
                (r'(?i)(api[_-]?key|secret[_-]?key|access[_-]?token|auth[_-]?token)[_-]?([a-z0-9]{32,})', FindingType.SECRET, 9, 0.8),
                (r'(?i)(aws[_-]?access[_-]?key[_-]?id|aws[_-]?secret[_-]?access[_-]?key)[_-]?([a-z0-9]{20,})', FindingType.SECRET, 9, 0.8),
                (r'(?i)(github[_-]?token|gitlab[_-]?token|bitbucket[_-]?token)[_-]?([a-z0-9]{32,})', FindingType.SECRET, 8, 0.7),
                (r'(?i)(password|passwd|pwd)[\s:= \'"]{1,5}([a-zA-Z0-9!@#$%^&*()_+]{8,})', FindingType.SECRET, 7, 0.6),
                (r'(?i)(eval|setTimeout|setInterval)\s*\(\s*["\']([^"\']+)["\']', FindingType.PATTERN, 7, 0.6),
                (r'(?i)(document\.write|document\.writeln)\s*\(\s*["\']([^"\']+)["\']', FindingType.PATTERN, 6, 0.5),
                (r'(?i)(innerHTML|outerHTML)\s*=\s*["\']([^"\']+)["\']', FindingType.PATTERN, 7, 0.6),
                (r'(?i)(/api/v[0-9]+/[a-z0-9/_-]+)', FindingType.ENDPOINT, 6, 0.8),
                (r'(?i)(/graphql|/rest|/v[1-3])(?:/|$|\?|=)', FindingType.ENDPOINT, 5, 0.7),
            ]
            self.compiled_basic_patterns = [
                (re.compile(p_str), f_type, sev, conf) for p_str, f_type, sev, conf in self.basic_pattern_definitions
            ]
            log.info(f"Basic pattern scanning enabled with {len(self.basic_pattern_definitions)} patterns")
        else:
            log.info("Basic pattern scanning disabled")
        
        self._content_hash_cache: Dict[str, Set[str]] = {}
        
        log.info(f"Initialized Scanner with {len(self.detectors)} detectors, TruffleHog enabled: {self.trufflehog_enabled}")
    
    async def scan(self, content: str, url: str, state: Optional[FullState] = None) -> FindingList:
        """Scan content for various types of findings.

        Args:
            content: The JavaScript content to scan
            url: The URL of the content being scanned
            state: Optional application state for tracking findings

        Returns:
            List of findings discovered in the content
        """
        if not content or not isinstance(content, str):
            log.warning(f"Invalid content provided for {url}")
            return []

        current_app_state = state or self.state

        content_hash = self._get_content_hash(content)
        self._update_content_hash_cache(content_hash, url)

        if self._should_skip_scanning(content_hash, url):
            if current_app_state is not None:
                first_url_with_content = next(iter(self._get_urls_with_same_content(content_hash)), None)
                if first_url_with_content and first_url_with_content in current_app_state.urls:
                    log.debug(f"Content of {url} is identical to previously scanned {first_url_with_content}. Copying findings.")
                    return current_app_state.urls[first_url_with_content].current_findings
            log.debug(f"Skipping scan for {url} due to identical content already processed, but no prior findings to copy (or state not available).")
            return []
        
        findings: FindingList = []
        
        try:
            basic_findings = self._scan_basic_patterns(content, url, current_app_state)
            findings.extend(basic_findings)
            log.debug(f"Found {len(basic_findings)} findings from basic patterns for {url}.")
        except Exception as e:
            log.error(f"Error during basic pattern scanning for {url}: {e}", exc_info=True)
        
        try:
            for detector_name, detector_instance in self.detectors.items():
                try:
                    log.debug(f"Running detector {detector_name} for {url}...")
                    detector_findings_dicts = detector_instance.scan_content(content, url) 
                    if detector_findings_dicts:
                        for finding_dict in detector_findings_dicts:
                            try:
                                mapped_type = self._map_detector_finding_type(detector_name, finding_dict)
                                reason_text = self._build_reason_from_detector_finding(detector_name, finding_dict)
                                preview_text = str(finding_dict.get('context', finding_dict.get('preview', ''))).strip().replace('\\n', ' ')
                                
                                finding_obj = Finding(
                                    type=mapped_type,
                                    reason=reason_text,
                                    line=int(finding_dict.get('line', 0)),
                                    preview=preview_text[:250],
                                    severity=_get_mapped_severity_int(finding_dict.get('severity')),
                                    confidence=float(finding_dict.get('confidence', 0.6))
                                )
                                
                                if current_app_state and url in current_app_state.urls:
                                    url_specific_state = current_app_state.urls[url]
                                    finding_content_for_hash = f"{finding_obj.type}:{finding_obj.reason}:{finding_obj.line}:{finding_obj.preview[:50]}"
                                    unique_finding_hash = self._get_content_hash(finding_content_for_hash)
                                    
                                    if not url_specific_state.is_new_finding(finding_obj.type, unique_finding_hash):
                                        continue 
                                    url_specific_state.mark_finding_seen(finding_obj.type, unique_finding_hash)
                                
                                findings.append(finding_obj)
                            except Exception as e:
                                log.error(f"Error mapping finding from {detector_name} for {url}: {e}", exc_info=True)
                        log.debug(f"Detector {detector_name} produced {len(detector_findings_dicts)} items for {url}, resulting in {len([f for f in findings if f not in basic_findings])} mapped findings.")
                except Exception as e:
                    log.error(f"Error running detector {detector_name} for {url}: {e}", exc_info=True)

            if findings:
                log.info(f"Total {len(findings)} findings (basic + detector) for {url}")
            return findings
            
        except Exception as e:
            log.error(f"Major error during detector scanning phase for {url}: {e}", exc_info=True)
            return findings

    def _build_reason_from_detector_finding(self, detector_name: str, finding_dict: Dict[str, Any]) -> str:
        if detector_name == "link_extractor":
            reason = f"Extracted Link: {finding_dict.get('link_url', 'Unknown link')}"
            if finding_dict.get('category'):
                reason += f" (Category: {finding_dict['category']})"
            return reason
        elif detector_name == "prototype_pollution" or detector_name == "template_injection":
            reason = str(finding_dict.get('description', 'Vulnerability detected'))
            if finding_dict.get('match'):
                match_str = str(finding_dict['match'])
                reason += f" (Match: {match_str[:50]}{'...' if len(match_str) > 50 else ''})"
            if finding_dict.get('template_engine'):
                reason += f" (Engine: {finding_dict.get('template_engine')})"
            return reason
        return str(finding_dict.get('reason', finding_dict.get('description', f'{detector_name} finding')))
    
    def _scan_basic_patterns(self, content: str, url: str, current_app_state: Optional[FullState] = None) -> FindingList:
        findings: FindingList = []
        for compiled_regex, finding_type, default_severity, default_confidence in self.compiled_basic_patterns:
            for match in compiled_regex.finditer(content):
                line_number = content.count('\n', 0, match.start()) + 1
                line_start_pos = content.rfind('\n', 0, match.start()) + 1
                line_end_pos = content.find('\n', match.start())
                if line_end_pos == -1: line_end_pos = len(content)
                
                preview_text = content[line_start_pos:line_end_pos].strip()
                
                try:
                    reason_detail = match.group(1) if match.groups() else match.group(0)
                except IndexError:
                    reason_detail = match.group(0)
                
                finding_obj = Finding(
                    type=finding_type,
                    reason=f"Basic pattern: {finding_type.value} - '{str(reason_detail)[:100]}'",
                    line=line_number,
                    preview=preview_text[:250],
                    severity=default_severity,
                    confidence=default_confidence
                )
                
                if current_app_state and url in current_app_state.urls:
                    url_specific_state = current_app_state.urls[url]
                    finding_content_for_hash = f"{finding_obj.type}:{finding_obj.reason}:{finding_obj.line}:{finding_obj.preview[:50]}"
                    unique_finding_hash = self._get_content_hash(finding_content_for_hash)
                    if not url_specific_state.is_new_finding(finding_obj.type, unique_finding_hash):
                        continue
                    url_specific_state.mark_finding_seen(finding_obj.type, unique_finding_hash)
                
                findings.append(finding_obj)
        return findings
    
    def _map_detector_finding_type(self, detector_name: str, finding: Dict[str, Any]) -> FindingType:
        if detector_name == 'link_extractor':
            return FindingType.LINK
        
        vuln_type_from_finding = finding.get('vulnerability_type')
        if vuln_type_from_finding == 'prototype_pollution' or detector_name == 'prototype_pollution':
            return FindingType.VULNERABILITY
        if vuln_type_from_finding == 'template_injection' or detector_name == 'template_injection':
            return FindingType.VULNERABILITY
        
        reported_type = str(finding.get('type', 'pattern')).upper()
        try:
            return FindingType[reported_type]
        except KeyError:
            log.warning(f"Unknown finding type '{reported_type}' from detector '{detector_name}'. Defaulting to PATTERN.")
            return FindingType.PATTERN

    async def scan_file_with_trufflehog(self, file_path: Path, original_url: str, current_app_state: Optional[FullState] = None) -> FindingList:
        findings: FindingList = []
        if not self.trufflehog_enabled or not self.trufflehog_command_path:
            log.debug(f"TruffleHog scanning skipped for {file_path} (disabled or not found).")
            return findings

        log.info(f"Running TruffleHog on: {file_path} (from URL: {original_url})")
        th_config = self.config.scanner.trufflehog

        command = [self.trufflehog_command_path, "filesystem", str(file_path)]
        command.extend(th_config.extra_args)

        try:
            with self.trufflehog_semaphore:
                # Use asyncio.create_subprocess_exec for better async control
                process = await asyncio.create_subprocess_exec(
                    *command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    cwd=None
                )

                # Track the process
                self._active_trufflehog_processes.add(process)

                try:
                    # Wait for process with timeout
                    stdout, stderr = await asyncio.wait_for(
                        process.communicate(),
                        timeout=th_config.timeout
                    )

                    # Decode output
                    stdout_text = stdout.decode('utf-8', errors='replace') if stdout else ""
                    stderr_text = stderr.decode('utf-8', errors='replace') if stderr else ""

                except asyncio.TimeoutError:
                    log.error(f"TruffleHog scan timed out for {file_path} after {th_config.timeout}s, killing process")
                    try:
                        process.kill()
                        await asyncio.wait_for(process.wait(), timeout=5.0)
                    except asyncio.TimeoutError:
                        log.error(f"Failed to kill TruffleHog process for {file_path} within 5 seconds")
                    except Exception as kill_error:
                        log.error(f"Failed to kill TruffleHog process for {file_path}: {kill_error}")
                    raise subprocess.TimeoutExpired(command, th_config.timeout)
                finally:
                    # Always remove from tracking set
                    self._active_trufflehog_processes.discard(process)

                # Check return code
                if stderr_text and process.returncode != 0:
                    log.info(f"TruffleHog for {file_path} reported to stderr (RC={process.returncode}): {stderr_text.strip()[:500]}")

                for line in stdout_text.strip().splitlines():
                    if not line.strip():
                        continue
                    try:
                        th_finding_data = json.loads(line)
                        reason = f"TruffleHog: {th_finding_data.get('DetectorName', th_finding_data.get('DecoderName', 'Unknown Secret'))}"
                        preview = th_finding_data.get('Redacted', th_finding_data.get('Raw', "N/A"))[:250]
                        
                        line_num_str = th_finding_data.get("SourceMetadata", {}).get("Data", {}).get("Filesystem", {}).get("line", "0")
                        try:
                            line_num = int(line_num_str)
                        except ValueError:
                            log.info(f"TruffleHog returned non-integer line number '{line_num_str}' for a finding in {file_path}. Defaulting to 0.")
                            line_num = 0

                        finding_obj = Finding(
                            type=FindingType.SECRET,
                            reason=reason,
                            line=line_num,
                            preview=preview,
                            severity=_get_mapped_severity_int("critical"),
                            confidence=0.95
                        )
                        
                        if current_app_state and original_url in current_app_state.urls:
                            url_specific_state = current_app_state.urls[original_url]
                            finding_content_for_hash = f"{finding_obj.type}:{finding_obj.reason}:{finding_obj.line}:{finding_obj.preview[:50]}"
                            unique_finding_hash = self._get_content_hash(finding_content_for_hash)
                            if not url_specific_state.is_new_finding(finding_obj.type, unique_finding_hash):
                                continue
                            url_specific_state.mark_finding_seen(finding_obj.type, unique_finding_hash)
                        
                        findings.append(finding_obj)
                    except json.JSONDecodeError:
                        log.info(f"Could not parse TruffleHog JSON line for {file_path}: '{line[:100]}...'")
        except subprocess.TimeoutExpired:
            log.error(f"TruffleHog scan timed out for {file_path} after {th_config.timeout}s")
        except Exception as e:
            log.error(f"Error running TruffleHog on {file_path}: {e}", exc_info=True)
        
        if findings:
            log.info(f"TruffleHog found {len(findings)} potential secrets in {file_path}")
        else:
            log.info(f"TruffleHog found no secrets in {file_path}")
        return findings

    def _get_content_hash(self, content: str) -> str:
        return hashlib.sha256(content.encode('utf-8', 'replace')).hexdigest()
        
    def _update_content_hash_cache(self, content_hash: str, url: str) -> None:
        if content_hash not in self._content_hash_cache:
            self._content_hash_cache[content_hash] = set()
        self._content_hash_cache[content_hash].add(url)
        
    def _get_urls_with_same_content(self, content_hash: str) -> Set[str]:
        return self._content_hash_cache.get(content_hash, set())
        
    def _should_skip_scanning(self, content_hash: str, url: str) -> bool:
        if content_hash in self._content_hash_cache:
            if url in self._content_hash_cache[content_hash] and len(self._content_hash_cache[content_hash]) > 1:
                first_url_seen = next((u for u in self._content_hash_cache[content_hash] if u != url), None)
                if first_url_seen:
                    log.debug(f"Skipping scanning for {url} - identical content to previously scanned {first_url_seen} (hash: {content_hash[:8]}...).")
                    return True
            elif url not in self._content_hash_cache[content_hash] and len(self._content_hash_cache[content_hash]) > 0:
                first_url_seen = next(iter(self._content_hash_cache[content_hash]))
                log.debug(f"Skipping scanning for {url} - identical content to previously scanned {first_url_seen} (hash: {content_hash[:8]}...).")
                return True
        return False

    async def cleanup_hanging_processes(self) -> None:
        """Kill any hanging TruffleHog processes."""
        if not self._active_trufflehog_processes:
            return

        log.warning(f"Cleaning up {len(self._active_trufflehog_processes)} hanging TruffleHog processes")

        for process in list(self._active_trufflehog_processes):
            try:
                if process.returncode is None:  # Process is still running
                    log.warning(f"Killing hanging TruffleHog process (PID: {process.pid})")
                    process.kill()
                    try:
                        await asyncio.wait_for(process.wait(), timeout=5.0)
                    except asyncio.TimeoutError:
                        log.error(f"Failed to kill TruffleHog process {process.pid} within 5 seconds")
                self._active_trufflehog_processes.discard(process)
            except Exception as e:
                log.error(f"Error cleaning up TruffleHog process: {e}")

        self._active_trufflehog_processes.clear()

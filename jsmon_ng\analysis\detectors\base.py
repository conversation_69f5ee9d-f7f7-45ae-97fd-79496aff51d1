"""
Base detector module for JSMon-NG.

This module provides the base classes and types for all detectors.
"""

from typing import Dict, List, Any, Optional
from pydantic import BaseModel

class BaseDetector:
    """Base class for all detectors."""
    
    def __init__(self, config: Any):
        """Initialize the detector with configuration.
        
        Args:
            config: Configuration object for the detector
        """
        self.config = config
        self.enabled = True
    
    def scan_content(self, content: str, url: str) -> List[Dict[str, Any]]:
        """Scan content for findings.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of finding dictionaries. Each dictionary should contain:
            - type: str (will be mapped to FindingType by Scanner)
            - line: int (line number where finding was detected)
            - context: str (line content or context)
            - severity: str or int (will be mapped to 0-10 scale by Scanner)
            - confidence: float (0.0-1.0)
            - description: str (detailed description of the finding)
            - details: Optional[Dict[str, Any]] (additional detector-specific details)
        """
        raise NotImplementedError("Detectors must implement scan_content") 
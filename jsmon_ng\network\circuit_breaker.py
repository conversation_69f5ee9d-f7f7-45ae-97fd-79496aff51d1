"""Custom circuit breaker implementation for JSMon-NG.

This module provides a more granular circuit breaker implementation than the
standard circuitbreaker library, with features like:
- Per-host circuit breaking
- Programmatic state management
- Custom failure counting
- Detailed state tracking
"""

import time
import logging
from enum import Enum
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass, field

log = logging.getLogger(__name__)

class CircuitState(str, Enum):
    """Possible states of a circuit breaker."""
    CLOSED = "closed"  # Normal operation, requests allowed
    OPEN = "open"  # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class CircuitStats:
    """Statistics for a circuit breaker."""
    total_requests: int = 0
    failed_requests: int = 0
    last_failure_time: Optional[float] = None
    last_success_time: Optional[float] = None
    current_failure_streak: int = 0
    max_failure_streak: int = 0
    state_changes: int = 0
    total_time_open: float = 0.0
    last_state_change: float = field(default_factory=time.time)

class CircuitBreaker:
    """Custom circuit breaker implementation with per-host tracking."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 30.0,
        half_open_timeout: float = 5.0,
        expected_exception: type = Exception
    ):
        """Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Seconds to wait before attempting recovery
            half_open_timeout: Seconds to wait in half-open state
            expected_exception: Exception type(s) that count as failures
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_timeout = half_open_timeout
        self.expected_exception = expected_exception
        
        # Per-host circuit state
        self._circuits: Dict[str, CircuitState] = {}
        self._stats: Dict[str, CircuitStats] = {}
        self._failure_counts: Dict[str, int] = {}
        self._last_failure_times: Dict[str, float] = {}
        self._half_open_times: Dict[str, float] = {}
        
    def _get_circuit(self, host: str) -> CircuitState:
        """Get current circuit state for a host."""
        return self._circuits.get(host, CircuitState.CLOSED)
        
    def _set_circuit(self, host: str, state: CircuitState) -> None:
        """Set circuit state for a host."""
        if host not in self._stats:
            self._stats[host] = CircuitStats()
            
        current_state = self._get_circuit(host)
        if current_state != state:
            self._stats[host].state_changes += 1
            self._stats[host].last_state_change = time.time()
            if current_state == CircuitState.OPEN:
                self._stats[host].total_time_open += (
                    time.time() - self._last_failure_times.get(host, time.time())
                )
                
        self._circuits[host] = state
        
    def _record_failure(self, host: str) -> None:
        """Record a failure for a host."""
        if host not in self._stats:
            self._stats[host] = CircuitStats()
            
        stats = self._stats[host]
        stats.failed_requests += 1
        stats.current_failure_streak += 1
        stats.max_failure_streak = max(stats.max_failure_streak, stats.current_failure_streak)
        stats.last_failure_time = time.time()
        self._last_failure_times[host] = time.time()
        
        self._failure_counts[host] = self._failure_counts.get(host, 0) + 1
        
    def _record_success(self, host: str) -> None:
        """Record a success for a host."""
        if host not in self._stats:
            self._stats[host] = CircuitStats()
            
        stats = self._stats[host]
        stats.current_failure_streak = 0
        stats.last_success_time = time.time()
        
    def _should_open_circuit(self, host: str) -> bool:
        """Check if circuit should be opened for a host."""
        return (
            self._failure_counts.get(host, 0) >= self.failure_threshold and
            time.time() - self._last_failure_times.get(host, 0) < self.recovery_timeout
        )
        
    def _should_attempt_recovery(self, host: str) -> bool:
        """Check if recovery should be attempted for a host."""
        return (
            self._get_circuit(host) == CircuitState.OPEN and
            time.time() - self._last_failure_times.get(host, 0) >= self.recovery_timeout
        )
        
    def _should_close_circuit(self, host: str) -> bool:
        """Check if circuit should be closed for a host."""
        if host not in self._half_open_times:
            return False
        return time.time() - self._half_open_times[host] >= self.half_open_timeout
        
    def allow_request(self, host: str) -> bool:
        """Check if a request should be allowed for a host.
        
        Args:
            host: Host to check circuit state for
            
        Returns:
            bool: True if request should be allowed, False if blocked
        """
        current_state = self._get_circuit(host)
        
        if current_state == CircuitState.CLOSED:
            return True
            
        if current_state == CircuitState.OPEN:
            if self._should_attempt_recovery(host):
                self._set_circuit(host, CircuitState.HALF_OPEN)
                self._half_open_times[host] = time.time()
                return True
            return False
            
        # HALF_OPEN state
        if self._should_close_circuit(host):
            self._set_circuit(host, CircuitState.CLOSED)
            self._failure_counts[host] = 0
            return True
        return False
        
    def on_success(self, host: str) -> None:
        """Handle successful request for a host.
        
        Args:
            host: Host that had successful request
        """
        self._record_success(host)
        if self._get_circuit(host) == CircuitState.HALF_OPEN:
            self._set_circuit(host, CircuitState.CLOSED)
            self._failure_counts[host] = 0
            
    def on_failure(self, host: str, exc: Exception) -> None:
        """Handle failed request for a host.
        
        Args:
            host: Host that had failed request
            exc: Exception that caused the failure
        """
        if not isinstance(exc, self.expected_exception):
            return
            
        self._record_failure(host)
        if self._should_open_circuit(host):
            self._set_circuit(host, CircuitState.OPEN)
            
    def get_stats(self, host: str) -> Optional[CircuitStats]:
        """Get circuit statistics for a host.
        
        Args:
            host: Host to get stats for
            
        Returns:
            Optional[CircuitStats]: Circuit statistics if available
        """
        return self._stats.get(host)
        
    def reset(self, host: str) -> None:
        """Reset circuit breaker state for a host.
        
        Args:
            host: Host to reset
        """
        self._set_circuit(host, CircuitState.CLOSED)
        self._failure_counts[host] = 0
        if host in self._stats:
            self._stats[host].current_failure_streak = 0
            
    def reset_all(self) -> None:
        """Reset all circuit breakers."""
        for host in list(self._circuits.keys()):
            self.reset(host) 
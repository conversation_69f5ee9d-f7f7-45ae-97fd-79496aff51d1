"""Differ module for JSMon-NG.

This module provides functionality for generating diffs between content versions,
with support for prettification and noise reduction.
"""

import asyncio
import difflib
import functools
import logging
import subprocess
import re
import time
from typing import List, Optional, Tu<PERSON>, Pattern
from pathlib import Path
from tempfile import NamedTemporaryFile

from ..config.config import AppConfig

log = logging.getLogger(__name__)

def _is_only_whitespace_change(diff_lines: List[str]) -> bool:
    """Checks if a diff only contains whitespace changes.

    Args:
        diff_lines: List of diff lines to check

    Returns:
        True if only whitespace changes are found, False otherwise
    """
    for line in diff_lines:
        if line.startswith(('---', '+++', '@@ ')):
            continue
        if line.startswith('+') or line.startswith('-'):
            if line[1:].strip():
                return False  # Found non-whitespace change
    return True  # Only whitespace changes found

async def _run_prettier(content: str, config: AppConfig) -> Optional[str]:
    """Runs prettier on the given content string.

    Args:
        content: The content to prettify
        config: Application configuration

    Returns:
        Prettified content or None if prettification fails/is skipped
    """
    prettify_cfg = config.differ.prettify_before_diff
    if not prettify_cfg.enabled:
        return None

    # Check for HTML content if skip_html_content is enabled
    if prettify_cfg.skip_html_content and bool(content and re.match(r'^\s*<', content)):
        log.debug("Skipping Prettier as content appears to be HTML and skip_html_content is enabled")
        return None

    prettier_cmd = str(prettify_cfg.prettier_path)
    timeout = prettify_cfg.timeout
    command = [
        prettier_cmd,
        "--stdin",
        "--stdin-filepath", "dummy.js",
        "--print-width", "80",
        "--tab-width", "2",
        "--single-quote",
        "--trailing-comma", "all",
        "--end-of-line", "lf"
    ]

    log.debug(f"Running prettier: {' '.join(command)}")
    start_time = time.perf_counter()
    try:
        # Use asyncio.run_in_executor to run subprocess.run in a thread pool
        loop = asyncio.get_running_loop()
        process_func = functools.partial(
            subprocess.run,
            command,
            input=content,
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=timeout,
            check=True
        )
        process = await loop.run_in_executor(None, process_func)
        elapsed = (time.perf_counter() - start_time) * 1000
        log.debug(f"Prettier formatting successful (stdin) in {elapsed:.1f}ms")
        return process.stdout
    except FileNotFoundError:
        log.error(f"Prettier command '{prettier_cmd}' not found. Cannot prettify. Check installation and differ.prettify_before_diff.prettier_path config. Consider 'npm install -g prettier' or ensure it's in your PATH.")
        return None
    except subprocess.TimeoutExpired:
        log.error(f"Prettier command timed out after {timeout}s.")
        return None
    except subprocess.CalledProcessError as e:
        log.error(f"Prettier command failed (Exit Code {e.returncode}): {e.stderr[:500]}...")
        return None
    except Exception as e:
        log.exception(f"Unexpected error running prettier: {e}")
        return None

async def _run_native_diff(old_lines: List[str], new_lines: List[str], context: int, ignore_patterns: List[Pattern]) -> Optional[List[str]]:
    """Run native diff command for better performance on large files.

    Note: For native diff, ignore_patterns (via -I) ignore matching lines from the input files,
    preventing them from being part of the diff analysis. This differs from the Python difflib path,
    which filters lines from the diff *output*.
    """
    old_path, new_path = None, None
    try:
        with NamedTemporaryFile(mode='w', suffix='.js', delete=False, encoding='utf-8') as old_file, \
             NamedTemporaryFile(mode='w', suffix='.js', delete=False, encoding='utf-8') as new_file:

            # Write content to temp files
            old_file.writelines(old_lines)
            new_file.writelines(new_lines)
            old_path = old_file.name
            new_path = new_file.name

        # Build diff command with ignore patterns
        cmd = ["diff", "-u", "-U", str(context), "--ignore-all-space", "--ignore-blank-lines"]
        for pattern in ignore_patterns:
            cmd.extend(["-I", pattern.pattern])
        cmd.extend([old_path, new_path])

        # Run diff
        start_time = time.perf_counter()
        # Use asyncio.run_in_executor to run subprocess.run in a thread pool
        loop = asyncio.get_running_loop()
        process_func = functools.partial(
            subprocess.run,
            cmd,
            capture_output=True,
            text=True,
            encoding='utf-8',
            check=False  # diff returns 1 for differences, 2 for errors
        )
        process = await loop.run_in_executor(None, process_func)
        elapsed = (time.perf_counter() - start_time) * 1000
        log.debug(f"Native diff completed in {elapsed:.1f}ms")

        # diff returns 0 for no changes, 1 for changes, >1 for errors
        if process.returncode > 1:
            log.error(f"Native diff command encountered an error (exit code {process.returncode}): {process.stderr}")
            return None

        # Return empty list if no differences (returncode 0)
        if process.returncode == 0:
            return []

        return process.stdout.splitlines(keepends=True)

    except FileNotFoundError:
        log.error("Native diff command ('diff') not found. Ensure it is installed and in PATH.")
        return None
    except Exception as e:
        log.error(f"Error running native diff: {e}")
        return None
    finally:
        # Ensure temp files are cleaned up
        for path in (old_path, new_path):
            if path and Path(path).exists():
                try:
                    Path(path).unlink()
                except Exception as e:
                    log.warning(f"Failed to clean up temp file {path}: {e}")

async def generate_diff(old_content: str, new_content: str, url: str, config: AppConfig) -> Tuple[List[str], bool]:
    """
    Generates a unified diff, optionally running prettier first,
    and applying noise reduction rules.
    Returns tuple: (diff_lines_list, was_prettified_bool).
    Empty list for diff_lines if diff should be ignored.
    """
    differ_cfg = config.differ
    prettify_cfg = differ_cfg.prettify_before_diff
    ignore_whitespace = differ_cfg.ignore_whitespace_only_changes
    min_diff_lines_threshold = differ_cfg.min_diff_lines
    diff_context_lines = differ_cfg.diff_context
    ignore_patterns = differ_cfg.compiled_patterns  # Use pre-compiled patterns
    native_diff_threshold = differ_cfg.native_diff_threshold

    # Split content once
    old_lines = old_content.splitlines(keepends=True)
    new_lines = new_content.splitlines(keepends=True)
    was_prettified = False

    if prettify_cfg.enabled:
        log.debug(f"Attempting to prettify content for diff: {url}")
        pretty_old = await _run_prettier(old_content, config)
        pretty_new = await _run_prettier(new_content, config)

        if pretty_old is not None and pretty_new is not None:
            log.debug(f"Using prettified content for diff: {url}")
            old_lines = pretty_old.splitlines(keepends=True)
            new_lines = pretty_new.splitlines(keepends=True)
            was_prettified = True
        else:
            log.warning(f"Prettifying failed for {url} (one or both contents). Falling back to original content for diff.")
    log.debug(f"Generating diff for {url} (IgnoreWS: {ignore_whitespace}, MinLines: {min_diff_lines_threshold}, Context: {diff_context_lines}, Prettified: {was_prettified}, IgnorePatterns: {len(ignore_patterns)})")

    # Try native diff first for better performance
    if len(old_lines) > native_diff_threshold or len(new_lines) > native_diff_threshold:
        diff_lines = await _run_native_diff(old_lines, new_lines, diff_context_lines, ignore_patterns)
        if diff_lines is not None:
            if not diff_lines:
                log.debug(f"No textual difference found for {url} (after potential processing).")
                return [], was_prettified
            return diff_lines, was_prettified

    # Fall back to Python's difflib for smaller files or if native diff fails
    try:
        start_time = time.perf_counter()
        raw_diff_lines = list(difflib.unified_diff(
            old_lines,
            new_lines,
            fromfile=f"{url} (old)",
            tofile=f"{url} (new)",
            lineterm='\n',
            n=diff_context_lines 
        ))
        elapsed = (time.perf_counter() - start_time) * 1000
        log.debug(f"Python difflib completed in {elapsed:.1f}ms")

        if not raw_diff_lines:
            log.debug(f"No textual difference found for {url} (after potential processing).")
            return [], was_prettified

        # Filter out lines matching ignore_line_patterns
        if ignore_patterns:
            filtered_diff = []
            for line in raw_diff_lines:
                if line.startswith(('---', '+++', '@@ ')):
                    filtered_diff.append(line)
                    continue
                if any(pattern.match(line) for pattern in ignore_patterns):
                    log.debug(f"Ignoring diff line due to pattern match: {line.strip()[:100]}")
                    continue
                filtered_diff.append(line)
            
            diff_lines_to_check = filtered_diff
            if len(raw_diff_lines) != len(filtered_diff):
                log.info(f"Filtered {len(raw_diff_lines) - len(filtered_diff)} lines from diff for {url} based on ignore_line_patterns.")
        else:
            diff_lines_to_check = raw_diff_lines

        # Check if diff is effectively empty
        is_effectively_empty = True
        for line in diff_lines_to_check:
            if not line.startswith(('---', '+++', '@@ ')):
                is_effectively_empty = False
                break
        if is_effectively_empty:
            log.info(f"Diff for {url} became empty after applying ignore_line_patterns. Ignoring.")
            return [], was_prettified

        # Apply noise reduction
        if ignore_whitespace and _is_only_whitespace_change(diff_lines_to_check):
            log.info(f"Ignoring diff for {url}: Only whitespace changes detected (after potential processing/filtering).")
            return [], was_prettified

        if min_diff_lines_threshold > 0:
            changed_line_count = sum(1 for line in diff_lines_to_check 
                                   if (line.startswith('+') or line.startswith('-')) 
                                   and not (line.startswith('---') or line.startswith('+++')))
            if changed_line_count < min_diff_lines_threshold:
                log.info(f"Ignoring diff for {url}: Changed lines ({changed_line_count}) below threshold ({min_diff_lines_threshold}) (after potential processing/filtering).")
                return [], was_prettified

        log.debug(f"Diff generated for {url} with {len(diff_lines_to_check)} lines (passed noise reduction).")
        return diff_lines_to_check, was_prettified

    except Exception as e:
        log.exception(f"Error generating diff for {url}: {e}")
        return [f"--- Error generating diff: {e}\n"], was_prettified
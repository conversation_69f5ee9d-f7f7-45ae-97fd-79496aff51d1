[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "jsmon-ng"
version = "1.3.0"
description = "Advanced JavaScript monitoring tool for detecting changes, secrets, patterns, and security vulnerabilities"
readme = "README.md"
license = { text = "MIT" }
authors = [
    { name = "sl4x0", email = "<EMAIL>" }
]
maintainers = [
    { name = "JSMon-NG Team", email = "<EMAIL>" }
]
requires-python = ">=3.9"
keywords = [
    "javascript", "monitoring", "security", "secrets", "vulnerability-scanner",
    "web-security", "static-analysis", "endpoint-discovery"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Console",
    "Intended Audience :: Developers",
    "Intended Audience :: Information Technology",
    "Intended Audience :: System Administrators",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP",
    "Topic :: Security",
    "Topic :: Software Development :: Quality Assurance",
    "Topic :: System :: Monitoring",
    "Topic :: Utilities",
]

dependencies = [
    # Core dependencies
    "click>=8.1.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "PyYAML>=6.0.1",
    "python-dotenv>=1.0.0",
    "filelock>=3.13.0",
    "aiofiles>=23.2.1",

    # HTTP and networking
    "httpx>=0.25.0",
    "tenacity>=8.2.0",
    "circuitbreaker>=1.4.0",

    # UI and formatting
    "rich>=13.7.0",
    "colorama>=0.4.6",

    # Metrics and monitoring
    "prometheus-client>=0.19.0",
    "psutil>=5.9.6",
]

[project.optional-dependencies]
# Development dependencies
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0",
]

# Testing dependencies
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "httpx-mock>=0.10.0",
    "respx>=0.20.0",
]

# Documentation dependencies
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.4.0",
    "mkdocstrings[python]>=0.23.0",
]

# Performance optimization dependencies
performance = [
    "orjson>=3.9.0",
    "uvloop>=0.19.0; sys_platform != 'win32'",
    "aiodns>=3.1.0",
]

# All optional dependencies
all = [
    "jsmon-ng[dev,test,docs,performance]"
]

[project.urls]
Homepage = "https://github.com/sl4x0/jsmon-ng"
Documentation = "https://jsmon-ng.readthedocs.io"
Repository = "https://github.com/sl4x0/jsmon-ng.git"
"Bug Tracker" = "https://github.com/sl4x0/jsmon-ng/issues"
Changelog = "https://github.com/sl4x0/jsmon-ng/blob/main/CHANGELOG.md"

[project.scripts]
jsmon-ng = "jsmon_ng.core.cli:cli"

[tool.setuptools.packages.find]
where = ["."]
include = ["jsmon_ng*"]
exclude = ["tests*", "docs*", "examples*"]

[tool.setuptools.package-data]
jsmon_ng = ["*.yaml", "*.yml", "*.json", "py.typed"]

# Black configuration
[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "circuitbreaker.*",
    "prometheus_client.*",
    "colorama.*",
]
ignore_missing_imports = true

# Pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=jsmon_ng",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "network: marks tests that require network access",
]

# Coverage configuration
[tool.coverage.run]
source = ["jsmon_ng"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

"""Notification manager for JSMon-NG.

This module provides:
- Factory function to create notification handlers
- Top-level notify_all function to send notifications
- Shutdown function to close all handlers
"""

import asyncio
import logging
from typing import List, Optional

from jsmon_ng.config.config import AppConfig
from jsmon_ng.notifications.discord import <PERSON><PERSON><PERSON><PERSON><PERSON>, DiscordConfig
from jsmon_ng.notifications.telegram import <PERSON><PERSON>ram<PERSON><PERSON><PERSON>, TelegramConfig
from jsmon_ng.notifications.handler import NotificationHandler
from jsmon_ng.state.types import HistoryEntry

logger = logging.getLogger(__name__)

_handlers: List[NotificationHandler] = []

def make_handlers(cfg: AppConfig) -> List[NotificationHandler]:
    """Create notification handlers based on configuration.

    Args:
        cfg: Application configuration

    Returns:
        List[NotificationHandler]: List of enabled notification handlers
    """
    handlers = []
    if cfg.notifications.discord.enabled:
        handlers.append(DiscordHandler(DiscordConfig(**cfg.notifications.discord.dict()),
                                      **cfg.notifications.retry.dict()))
    if cfg.notifications.telegram.enabled:
        handlers.append(TelegramHandler(TelegramConfig(**cfg.notifications.telegram.dict()),
                                       **cfg.notifications.retry.dict()))
    return handlers

def init_handlers(cfg: AppConfig):
    """Initialize notification handlers based on configuration.

    Args:
        cfg: Application configuration
    """
    global _handlers
    _handlers = make_handlers(cfg)
    logger.info("Initialized %d notification handlers", len(_handlers))

async def notify_all(
    cfg: AppConfig,
    *,
    title: str,
    message: str,
    url: str,
    old_entry: Optional[HistoryEntry] = None,
    new_entry: Optional[HistoryEntry] = None,
) -> None:
    """Send a notification to all enabled handlers.

    Args:
        cfg: Application configuration
        title: Notification title
        message: Notification message
        url: URL that triggered the notification
        old_entry: Previous history entry
        new_entry: New history entry
    """
    if not _handlers:
        logger.warning("No notification handlers are enabled; skipping notify_all()")
        return
    coros = [h.send_notification(title, message, url, old_entry=old_entry, new_entry=new_entry) for h in _handlers]
    results = await asyncio.gather(*coros, return_exceptions=True)
    for h, r in zip(_handlers, results):
        if isinstance(r, Exception):
            logger.exception(f"{h.__class__.__name__} blew up: {r}")
        elif r is False:
            logger.error(f"{h.__class__.__name__} returned False")

async def shutdown():
    """Close all notification handlers."""
    for h in _handlers:
        await h.close()
    _handlers.clear() 
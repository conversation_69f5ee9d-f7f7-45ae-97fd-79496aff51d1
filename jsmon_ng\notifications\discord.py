"""Discord notification handler for JSMon-NG.

This module provides:
- Discord webhook integration
- Rich message formatting
- Error handling and retries
"""

import logging
from typing import Any, Dict, Optional
from datetime import datetime, timezone

import aiohttp
from pydantic import BaseModel, HttpUrl

from jsmon_ng.notifications.formatter import EnhancedNotificationFormatter
from jsmon_ng.notifications.handler import Notification<PERSON>andler
from jsmon_ng.notifications.payloads import build_discord_embed, build_discord_payload
from jsmon_ng.state.types import HistoryEntry

logger = logging.getLogger(__name__)

class DiscordConfig(BaseModel):
    """Discord notification configuration."""

    webhook_url: HttpUrl
    username: Optional[str] = None
    avatar_url: Optional[HttpUrl] = None
    color: int = 0x3498DB  # Default to a nice blue color
    include_preview: bool = True
    max_preview_length: int = 1000

class DiscordHandler(NotificationHandler):
    """Discord notification handler."""

    def __init__(
        self,
        config: DiscordConfig,
        **backoff_kwargs: Any,
    ):
        super().__init__(**backoff_kwargs)
        self.config = config
        self.formatter = EnhancedNotificationFormatter()

    def url_in_message(self, url: str, message: str) -> bool:
        """Checks if the URL is already likely part of the message."""
        if url in message:
            return True
        if message.startswith(f"New findings detected for {url}"): # Specific check for "New Findings" pattern
            return True
        return False

    async def send_notification(
        self,
        title: str,
        message: str,
        url: str,
        old_entry: Optional[HistoryEntry] = None,
        new_entry: Optional[HistoryEntry] = None,
        **kwargs: Any,
    ) -> bool:
        """Send a notification to Discord.

        Args:
            title: Notification title for the embed.
            message: Main text message or description for the embed.
            url: URL that triggered the notification, often part of the message.
            old_entry: Previous history entry (for diff notifications).
            new_entry: New history entry (for diff or new item notifications).
            **kwargs: Additional notification-specific arguments.

        Returns:
            bool: True if notification was sent successfully.
        """
        try:
            embed_description: str
            if old_entry and new_entry:
                _, text_version_of_diff = self.formatter.format_diff_notification(
                    old_entry, new_entry, url
                )
                embed_description = text_version_of_diff
                if self.config.max_preview_length and len(embed_description) > self.config.max_preview_length:
                    embed_description = embed_description[:self.config.max_preview_length - 3] + "..."
            else:
                # For general messages like "New Findings"
                if self.config.include_preview and url and not self.url_in_message(url, message):
                    embed_description = f"{message}\n\nURL: {url}"
                else:
                    embed_description = message

                # Ensure embed description doesn't exceed Discord's limits
                effective_max_length = self.config.max_preview_length if self.config.max_preview_length > 0 else 4090
                if len(embed_description) > effective_max_length:
                    embed_description = embed_description[:effective_max_length - 3] + "..."
                    logger.warning(
                        f"Discord embed description for {url} (title: '{title}') was truncated "
                        f"to ~{effective_max_length} chars due to excessive length."
                    )

            fields = []
            if new_entry:
                fields.extend([
                    {"name": "Size", "value": self._format_size(new_entry.size), "inline": True},
                    {"name": "Hash", "value": self._format_hash(new_entry.hash), "inline": True},
                    {"name": "Risk Score", "value": self._format_risk_score(new_entry.risk_score), "inline": True},
                ])
                if new_entry.findings:
                    fields.append({"name": "Findings Count", "value": str(len(new_entry.findings)), "inline": True})

            embed = build_discord_embed(
                title=title,
                description=embed_description,
                color=self.config.color,
                fields=fields,
                footer_text="jsmon-ng",
                timestamp=datetime.now(timezone.utc).isoformat()
            )

            payload = build_discord_payload(
                embeds=[embed],
                username=self.config.username,
                avatar_url=self.config.avatar_url,
            )

            await self._retry_with_backoff(self._send_webhook, payload.model_dump(exclude_none=True))
            logger.debug(f"Discord notification sent for {url} with title '{title}'")
            return True

        except Exception as e:
            logger.error(f"[DiscordHandler] Failed to send notification for {url}: {e}", exc_info=True)
            return False

    async def _send_webhook(self, payload_dict: Dict[str, Any]) -> None:
        """Send a webhook to Discord.

        Args:
            payload_dict: Webhook payload as a dictionary.

        Raises:
            aiohttp.ClientError: If the request fails.
        """
        session = await self._get_session()
        async with session.post(str(self.config.webhook_url), json=payload_dict) as response:
            if not response.ok:
                error_text = await response.text()
                logger.error(f"Discord API error ({response.status}): {error_text}. Payload: {payload_dict}")
            response.raise_for_status()

    def _format_size(self, size: int) -> str:
        if size < 1024:
            return f"{size} bytes"
        elif size < 1024 * 1024:
            return f"{size / 1024:.2f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.2f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.2f} GB"

    def _format_hash(self, hash: str) -> str:
        return hash[:8]

    def _format_risk_score(self, risk_score: float) -> str:
        return f"{risk_score:.2f}/100"
"""JSMon-NG Utilities Module.

This module provides utility functions and logging configuration for the application.

The module includes:
- Logging configuration with colored output and Rich support
- General utility functions for URL handling, file operations, and formatting
- Helper functions used across the application
"""

from .logging_config import (
    configure_logging,
    ColoredFormatter,
    LoggingSettings,
    RICH_AVAILABLE,
    COLOR_ENABLED,
)

from .utils import (
    normalize_url,
    is_valid_url,
    safe_filename,
    calculate_content_hash,
    truncate_string,
    format_bytes,
    extract_domain,
)

__all__ = [
    # Logging configuration
    "configure_logging",
    "ColoredFormatter",
    "LoggingSettings",
    "RICH_AVAILABLE",
    "COLOR_ENABLED",

    # General utilities
    "normalize_url",
    "is_valid_url",
    "safe_filename",
    "calculate_content_hash",
    "truncate_string",
    "format_bytes",
    "extract_domain",
]
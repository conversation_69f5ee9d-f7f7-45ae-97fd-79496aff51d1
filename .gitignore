# Python build artifacts
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
*~
.project
.pydevproject
.settings/

# Logs and databases
*.log
*.sqlite
*.db
jsmon.log
debug.log

# Backup files
*.bak
*.backup
*~
*.old
backup/
backups/
/backup/
/backups/
**/backup/
**/backups/

# State and cache files
jsmon_state.json
.cache/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
downloads/
recon_base_directory/
*.map
*.min.js
*.min.css
*.bundle.js
*.chunk.js

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Configuration files with secrets
.env
.env.*
*.pem
*.key
*.crt
*.cert
config.local.yaml
config.dev.yaml
config.prod.yaml

# Test coverage and reports
.coverage
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage/
.coverage.*
coverage.json
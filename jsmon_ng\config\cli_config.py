"""CLI configuration and environment variable handling for JSMon-NG."""

import os
import click
from functools import wraps
from typing import Callable, Any
from .config import AppConfig, load_config

# Environment variable prefixes
ENV_PREFIX = "JSN_"

# Environment variable mappings
ENV_MAPPINGS = {
    "FETCH_WORKERS": ("concurrency.max_fetch_workers", int),
    "DOMAIN_LIMIT": ("concurrency.max_concurrent_per_domain", int),
    "FETCH_TIMEOUT": ("fetcher.timeout", int),
    "RUN_INTERVAL": ("run_interval_seconds", int),
    "LOG_LEVEL": ("logging.level", str),
    "STATE_FILE": ("state_file", str),
    "DOWNLOAD_DIR": ("download_directory", str),
}

def get_env_value(key: str, default: Any = None) -> Any:
    """Get a value from environment variables with type conversion.
    
    Args:
        key: Environment variable key (without prefix)
        default: Default value if not found
        
    Returns:
        Converted value from environment variable or default
    """
    env_key = f"{ENV_PREFIX}{key}"
    value = os.environ.get(env_key)
    if value is None:
        return default
        
    # Get the type converter from mappings
    _, type_converter = ENV_MAPPINGS.get(key, (None, str))
    try:
        return type_converter(value)
    except (ValueError, TypeError):
        return default

def apply_env_overrides(config: AppConfig) -> AppConfig:
    """Apply environment variable overrides to configuration.
    
    Args:
        config: Base configuration object
        
    Returns:
        Updated configuration object
    """
    for env_key, (config_path, _) in ENV_MAPPINGS.items():
        value = get_env_value(env_key)
        if value is not None:
            # Split the path and traverse the config object
            parts = config_path.split('.')
            current = config
            for part in parts[:-1]:
                current = getattr(current, part)
            setattr(current, parts[-1], value)
            
    return config

def config_option(func: Callable) -> Callable:
    """Decorator to add config file option to commands.
    
    Args:
        func: Command function to decorate
        
    Returns:
        Decorated function
    """
    @click.option('-c', '--config-file',
                 default='config.yaml',
                 type=click.Path(exists=True, dir_okay=False, resolve_path=True),
                 show_default=True,
                 help='Path to the configuration file.')
    @wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    return wrapper

def get_config(config_file: str) -> AppConfig:
    """Load and process configuration with environment overrides.
    
    Args:
        config_file: Path to configuration file
        
    Returns:
        Processed configuration object
    """
    config = load_config(config_file)
    return apply_env_overrides(config)

def log_level_option(func: Callable) -> Callable:
    """Decorator to add log level option to commands.
    
    Args:
        func: Command function to decorate
        
    Returns:
        Decorated function
    """
    @click.option('--log-level',
                 type=click.Choice(['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                                 case_sensitive=False),
                 help='Override log level from config file.')
    @wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    return wrapper
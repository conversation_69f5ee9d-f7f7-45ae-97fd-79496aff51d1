"""Configuration module for JSMon-NG.

This module provides configuration management functionality including:
- Configuration models and validation
- CLI configuration handling
- Environment variable overrides
- Severity and risk scoring
"""

from .config import AppConfig, load_config
from .cli_config import get_config, apply_env_overrides, config_option, log_level_option
from .severity import (
    compute_risk_score,
    get_finding_weight,
    load_weights_from_config,
    SEVERITY_WEIGHTS,
    PATTERN_SEVERITY_WEIGHTS
)

__all__ = [
    'AppConfig',
    'load_config',
    'get_config',
    'apply_env_overrides',
    'config_option',
    'log_level_option',
    'compute_risk_score',
    'get_finding_weight',
    'load_weights_from_config',
    'SEVERITY_WEIGHTS',
    'PATTERN_SEVERITY_WEIGHTS'
]
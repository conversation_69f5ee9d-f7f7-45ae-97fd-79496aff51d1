"""Link extractor detector for JavaScript content analysis.

This module provides functionality to extract and analyze links from JavaScript code,
including URLs in various contexts like API calls, imports, and template literals.
"""

import re
import logging
import urllib.parse
from typing import Dict, List, Any, Set

# --- Project Modules ---
from jsmon_ng.config.config import AppConfig

log = logging.getLogger(__name__)

# Default configuration values
DEFAULT_MIN_URL_LENGTH = 8
DEFAULT_MAX_URL_LENGTH = 2048
DEFAULT_EXCLUDE_PATTERNS = [
    r'(?:example\.com|localhost|127\.0\.0\.1|0\.0\.0\.0|test\.com|testing\.com|dummy\.com)'
]

# Context extraction settings
CONTEXT_PADDING = 50  # Characters to include before and after the match

class LinkExtractor:
    """Detector for extracting and monitoring links in JavaScript code."""
    
    def __init__(self, config: AppConfig):
        """Initialize the extractor with configuration.
        
        Args:
            config: Application configuration containing link extraction settings
        """
        self.config = config
        self.scanner_config = config.scanner
        
        # Validate configuration
        if not hasattr(self.scanner_config, 'link_extraction'):
            raise ValueError("AppConfig.scanner.link_extraction is required")
        
        self.link_config = self.scanner_config.link_extraction
        self.enabled = self.link_config.enabled
        
        # Link extraction patterns
        self.patterns = [
            # URLs in quotes
            re.compile(r'(?:"|\'|\`)(?P<url>(?:https?://)[a-zA-Z0-9][a-zA-Z0-9\-]*(?:\.[a-zA-Z0-9\-]+)+(?:/[a-zA-Z0-9_\-\.~]+)*(?:\?[^\'"\s]+)?)(?:"|\'|\`)', re.IGNORECASE),
            
            # URLs in fetch/XHR calls
            re.compile(r'(?:fetch|xhr\.open|axios\.get|axios\.post|axios\.put|axios\.delete)\s*\(\s*(?:"|\'|\`)(?P<url>(?:https?://)[a-zA-Z0-9][a-zA-Z0-9\-]*(?:\.[a-zA-Z0-9\-]+)+(?:/[a-zA-Z0-9_\-\.~]+)*(?:\?[^\'"\s]+)?)(?:"|\'|\`)', re.IGNORECASE),
            
            # URLs in src/href attributes
            re.compile(r'(?:src|href|action)\s*=\s*(?:"|\'|\`)(?P<url>(?:https?://)[a-zA-Z0-9][a-zA-Z0-9\-]*(?:\.[a-zA-Z0-9\-]+)+(?:/[a-zA-Z0-9_\-\.~]+)*(?:\?[^\'"\s]+)?)(?:"|\'|\`)', re.IGNORECASE),
            
            # URLs in window.open/location
            re.compile(r'(?:window\.open|location\.href|location\.replace|location\.assign)\s*\(\s*(?:"|\'|\`)(?P<url>(?:https?://)[a-zA-Z0-9][a-zA-Z0-9\-]*(?:\.[a-zA-Z0-9\-]+)+(?:/[a-zA-Z0-9_\-\.~]+)*(?:\?[^\'"\s]+)?)(?:"|\'|\`)', re.IGNORECASE),
            
            # URLs in WebSocket connections
            re.compile(r'(?:WebSocket|new\s+WebSocket)\s*\(\s*(?:"|\'|\`)(?P<url>(?:wss?://)[a-zA-Z0-9][a-zA-Z0-9\-]*(?:\.[a-zA-Z0-9\-]+)+(?:/[a-zA-Z0-9_\-\.~]+)*(?:\?[^\'"\s]+)?)(?:"|\'|\`)', re.IGNORECASE),
            
            # URLs in EventSource
            re.compile(r'(?:EventSource|new\s+EventSource)\s*\(\s*(?:"|\'|\`)(?P<url>(?:https?://)[a-zA-Z0-9][a-zA-Z0-9\-]*(?:\.[a-zA-Z0-9\-]+)+(?:/[a-zA-Z0-9_\-\.~]+)*(?:\?[^\'"\s]+)?)(?:"|\'|\`)', re.IGNORECASE),
            
            # URLs in template literals
            re.compile(r'`(?P<url>(?:https?://)[a-zA-Z0-9][a-zA-Z0-9\-]*(?:\.[a-zA-Z0-9\-]+)+(?:/[a-zA-Z0-9_\-\.~]+)*(?:\?[^`\s]+)?)`', re.IGNORECASE),
            
            # URLs in import statements
            re.compile(r'import\s+(?:.+\s+from\s+)?(?:"|\'|\`)(?P<url>(?:https?://)[a-zA-Z0-9][a-zA-Z0-9\-]*(?:\.[a-zA-Z0-9\-]+)+(?:/[a-zA-Z0-9_\-\.~]+)*(?:\?[^\'"\s]+)?)(?:"|\'|\`)', re.IGNORECASE)
        ]
        
        # Exclude patterns from config or default
        self.exclude_patterns = [
            re.compile(pattern, re.IGNORECASE) 
            for pattern in getattr(self.link_config, 'exclude_patterns', DEFAULT_EXCLUDE_PATTERNS)
        ]
        
        # Categorization patterns
        self.api_patterns = [
            re.compile(r'/api/'),
            re.compile(r'/v[0-9]+/'),
            re.compile(r'graphql'),
            re.compile(r'\.json$')
        ]
        
        self.asset_patterns = [
            re.compile(r'\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$', re.IGNORECASE)
        ]
        
        self.auth_patterns = [
            re.compile(r'(?:auth|login|signin|signup|register|logout|password|reset)', re.IGNORECASE)
        ]
        
        # Track discovered links to identify new ones
        self.known_links: Set[str] = set()
        
        log.info(f"Initialized LinkExtractor with {len(self.patterns)} patterns and {len(self.exclude_patterns)} exclude patterns")
    
    def scan_content(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Extract and analyze links from JavaScript content.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of link findings
        """
        if not self.enabled:
            return []
        
        if not content or not isinstance(content, str):
            log.warning(f"Invalid content provided for {url}")
            return []
        
        findings = []
        new_links = []
        processed_normalized_urls: Set[str] = set()  # Use set for O(1) lookup
        
        try:
            for pattern in self.patterns:
                for match in pattern.finditer(content):
                    try:
                        link_url = match.group('url')
                        
                        # Skip if it matches exclude patterns
                        if any(exclude.search(link_url) for exclude in self.exclude_patterns):
                            continue
                        
                        # Validate URL length
                        min_length = getattr(self.link_config, 'min_url_length', DEFAULT_MIN_URL_LENGTH)
                        max_length = getattr(self.link_config, 'max_url_length', DEFAULT_MAX_URL_LENGTH)
                        if not min_length <= len(link_url) <= max_length:
                            continue
                        
                        # Normalize the URL
                        normalized_url = self._normalize_url(link_url)
                        
                        # Skip if already processed in this scan (O(1) lookup)
                        if normalized_url in processed_normalized_urls:
                            continue
                        
                        # Create finding
                        finding = self._create_finding(link_url, normalized_url, url, content, match)
                        findings.append(finding)
                        processed_normalized_urls.add(normalized_url)  # Add to set
                        
                        # Track if this is a new link
                        if normalized_url not in self.known_links:
                            self.known_links.add(normalized_url)
                            new_links.append(finding)
                            finding['is_new'] = True
                            
                    except Exception as e:
                        log.error(f"Error processing link match in {url}: {e}", exc_info=True)
                        continue
            
            if findings:
                log.info(f"Found {len(findings)} links in {url} ({len(new_links)} new)")
            
            return findings
            
        except Exception as e:
            log.error(f"Error scanning content from {url}: {e}", exc_info=True)
            return []
    
    def _normalize_url(self, url: str) -> str:
        """
        Normalize a URL for comparison and deduplication.
        
        Args:
            url: The URL to normalize
            
        Returns:
            Normalized URL
        """
        try:
            # Parse the URL
            parsed = urllib.parse.urlparse(url)
            
            # Normalize the hostname to lowercase
            hostname = parsed.netloc.lower()
            
            # Normalize the path
            path = parsed.path
            if not path:
                path = '/'
            
            # Remove trailing slashes
            while path.endswith('/') and len(path) > 1:
                path = path[:-1]
            
            # Sort query parameters
            if parsed.query:
                query_params = sorted(urllib.parse.parse_qsl(parsed.query))
                query = urllib.parse.urlencode(query_params)
            else:
                query = ''
            
            # Reconstruct the normalized URL
            normalized = urllib.parse.urlunparse((
                parsed.scheme,
                hostname,
                path,
                parsed.params,
                query,
                ''  # Remove fragment
            ))
            
            return normalized
        
        except Exception as e:
            log.debug(f"Error normalizing URL {url}: {e}")
            return url
    
    def _create_finding(self, link_url: str, normalized_url: str, source_url: str, 
                       content: str, match) -> Dict[str, Any]:
        """
        Create a finding for a link.
        
        Args:
            link_url: The discovered link URL
            normalized_url: Normalized version of the URL
            source_url: URL where the link was found
            content: The full content
            match: The regex match object
            
        Returns:
            Finding dictionary
        """
        try:
            line_number = content.count('\n', 0, match.start()) + 1
            
            # Get context around the match
            context_start = max(0, match.start() - CONTEXT_PADDING)
            context_end = min(len(content), match.end() + CONTEXT_PADDING)
            context = content[context_start:context_end].replace('\n', ' ')
            
            # Categorize the link
            category = self._categorize_link(link_url)
            
            # Determine severity based on category
            severity = "medium" if category in ['api', 'auth'] else "low"
            
            finding = {
                'type': 'link',
                'link_url': link_url,
                'normalized_url': normalized_url,
                'source_url': source_url,
                'line': line_number,
                'context': context,
                'category': category,
                'severity': severity,
                'confidence': 0.9,  # High confidence for direct extraction
                'is_new': False  # Will be set to True for new links
            }
            
            return finding
            
        except Exception as e:
            log.error(f"Error creating finding for {link_url} in {source_url}: {e}", exc_info=True)
            return {
                'type': 'link',
                'link_url': link_url,
                'normalized_url': normalized_url,
                'source_url': source_url,
                'line': 0,
                'context': '',
                'category': 'unknown',
                'severity': 'low',
                'confidence': 0.5,
                'is_new': False,
                'error': str(e)
            }
    
    def _categorize_link(self, url: str) -> str:
        """
        Categorize a link based on its URL pattern.
        
        Args:
            url: The URL to categorize
            
        Returns:
            Category string
        """
        try:
            if any(pattern.search(url) for pattern in self.api_patterns):
                return 'api'
            
            if any(pattern.search(url) for pattern in self.asset_patterns):
                return 'asset'
            
            if any(pattern.search(url) for pattern in self.auth_patterns):
                return 'auth'
            
            return 'general'
            
        except Exception as e:
            log.debug(f"Error categorizing URL {url}: {e}")
            return 'unknown'

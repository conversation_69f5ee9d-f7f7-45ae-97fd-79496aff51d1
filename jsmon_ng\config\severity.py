"""Severity and risk scoring module for JSMon-NG.

This module provides functionality for calculating risk scores based on findings
and managing severity weights for different types of findings.
"""

from typing import Dict, List, Any

# Constants for score normalization
MAX_SCORE = 100  # Maximum normalized score
MAX_WEIGHT_PER_FINDING = 100  # Cap individual finding contribution

# Default severity weights for different finding types
DEFAULT_SEVERITY_WEIGHTS = {
    "secret": 100,      # High severity
    "pattern": 50,      # Medium severity (default, can be overridden by rule)
    "endpoint": 10,     # Low severity
    "sourcemap_error": 5,  # Low severity for source map parsing errors
    # Add weights for other types if needed
}

# Default weights for specific pattern severities (optional override)
DEFAULT_PATTERN_SEVERITY_WEIGHTS = {
    "critical": 90,
    "high": 70,
    "medium": 50,
    "low": 20,
    "info": 5,
}

# Global variables that can be updated from config
SEVERITY_WEIGHTS = DEFAULT_SEVERITY_WEIGHTS.copy()
PATTERN_SEVERITY_WEIGHTS = DEFAULT_PATTERN_SEVERITY_WEIGHTS.copy()

def get_finding_weight(finding: Dict[str, Any]) -> int:
    """Gets the appropriate weight for a finding based on its type and severity.

    Args:
        finding: Dictionary containing finding information with 'type' and optionally 'severity'

    Returns:
        Weight value for the finding
    """
    finding_type = finding.get("type")
    base_weight = SEVERITY_WEIGHTS.get(finding_type, 0)

    # Override for patterns based on rule severity
    if finding_type == "pattern":
        pattern_severity = finding.get("severity", "Info").lower()
        base_weight = PATTERN_SEVERITY_WEIGHTS.get(pattern_severity, base_weight)  # Use base if severity unknown

    return base_weight

def compute_risk_score(findings: List[Dict[str, Any]]) -> int:
    """Calculates a normalized risk score based on the findings.

    Args:
        findings: List of finding dictionaries

    Returns:
        Normalized risk score between 0 and MAX_SCORE
    """
    if not findings:
        return 0

    # Cap each finding's weight, then sum
    raw_score = sum(
        min(get_finding_weight(f), MAX_WEIGHT_PER_FINDING)
        for f in findings
    )
    # Normalize to [0, MAX_SCORE]
    return min(raw_score, MAX_SCORE)


def load_weights_from_config(config: Any) -> None:
    """Updates severity weights from configuration if provided.

    This allows operators to customize severity weights without code changes.

    Args:
        config: Configuration object that may contain severity weight settings
    """
    global SEVERITY_WEIGHTS, PATTERN_SEVERITY_WEIGHTS

    # Check if config has severity weights section
    if hasattr(config, 'severity') and config.severity:
        # Update finding type weights if provided
        if hasattr(config.severity, 'type_weights') and config.severity.type_weights:
            for finding_type, weight in config.severity.type_weights.items():
                SEVERITY_WEIGHTS[finding_type] = int(weight)

        # Update pattern severity weights if provided
        if hasattr(config.severity, 'pattern_weights') and config.severity.pattern_weights:
            for severity_level, weight in config.severity.pattern_weights.items():
                PATTERN_SEVERITY_WEIGHTS[severity_level.lower()] = int(weight)
"""Common network utilities for JSMon-NG.

This module provides:
- FetchResult dataclass for storing fetch results
- Metrics collection using prometheus_client
- URL normalization utilities
- Stream processing utilities
"""

import hashlib
from dataclasses import dataclass
from typing import Dict, Optional, Tuple, Any, Iterable, NamedTuple
from urllib.parse import urlparse, parse_qsl, urlencode

from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry

from jsmon_ng.config.config import AppConfig

@dataclass
class FetchResult:
    """Result of a URL fetch operation."""
    text: Optional[str]
    sha256: Optional[str]
    size: Optional[int]
    headers: Optional[Dict[str, str]]
    status: int
    success: bool
    error: Optional[Dict[str, Any]]
    duration: float

class Metrics(NamedTuple):
    """Prometheus metrics for network operations."""
    fetch_total: Counter
    fetch_duration: Histogram
    fetch_size: Histogram
    fetch_errors: Counter
    fetch_cache_hits: Counter
    fetch_cache_misses: Counter
    fetch_retries: Counter
    fetch_circuit_breaker_trips: Counter
    fetch_circuit_breaker_resets: Counter
    fetch_circuit_breaker_state: Gauge

def create_metrics(registry: Optional[CollectorRegistry] = None) -> Metrics:
    """Create Prometheus metrics for network operations.
    
    Args:
        registry: Optional Prometheus collector registry
        
    Returns:
        Metrics instance with all counters and histograms
    """
    if registry is None:
        registry = CollectorRegistry()
        
    return Metrics(
        fetch_total=Counter(
            'jsmon_fetch_total',
            'Total number of fetch operations',
            ['method'],
            registry=registry
        ),
        fetch_duration=Histogram(
            'jsmon_fetch_duration_seconds',
            'Time spent fetching URLs',
            ['method'],
            registry=registry
        ),
        fetch_size=Histogram(
            'jsmon_fetch_size_bytes',
            'Size of fetched content',
            ['method'],
            registry=registry
        ),
        fetch_errors=Counter(
            'jsmon_fetch_errors_total',
            'Total number of fetch errors',
            ['method', 'error_type'],
            registry=registry
        ),
        fetch_cache_hits=Counter(
            'jsmon_fetch_cache_hits_total',
            'Total number of cache hits',
            ['method'],
            registry=registry
        ),
        fetch_cache_misses=Counter(
            'jsmon_fetch_cache_misses_total',
            'Total number of cache misses',
            ['method'],
            registry=registry
        ),
        fetch_retries=Counter(
            'jsmon_fetch_retries_total',
            'Total number of retry attempts',
            ['method'],
            registry=registry
        ),
        fetch_circuit_breaker_trips=Counter(
            'jsmon_fetch_circuit_breaker_trips_total',
            'Total number of circuit breaker trips',
            ['method'],
            registry=registry
        ),
        fetch_circuit_breaker_resets=Counter(
            'jsmon_fetch_circuit_breaker_resets_total',
            'Total number of circuit breaker resets',
            ['method'],
            registry=registry
        ),
        fetch_circuit_breaker_state=Gauge(
            'jsmon_fetch_circuit_breaker_state',
            'Current state of circuit breaker (0=closed, 1=open, 2=half-open)',
            ['method'],
            registry=registry
        )
    )

def normalize_url_for_cache(url: str, cfg: AppConfig) -> str:
    """Normalize URL for caching purposes.
    
    Args:
        url: URL to normalize
        cfg: Application configuration
        
    Returns:
        Normalized URL string
    """
    parsed = urlparse(url)  # urlparse can handle URLs without scheme initially
    fcfg = cfg.fetcher

    # Ensure scheme, default to https if normalizing or missing
    current_scheme = parsed.scheme
    if fcfg.cache_normalize_scheme or not current_scheme:
        parsed = parsed._replace(scheme='https')

    # Query parameter handling
    if fcfg.strip_query_params:
        parsed = parsed._replace(query='')
    elif fcfg.cache_normalize_query and parsed.query:
        items = sorted(parse_qsl(parsed.query))
        parsed = parsed._replace(query=urlencode(items))

    if fcfg.strip_fragments_for_cache_key and parsed.fragment:
        parsed = parsed._replace(fragment='')

    return parsed.geturl()

def get_host(url: str) -> str:
    """Extract host from URL.
    
    Args:
        url: URL to parse
        
    Returns:
        Host string
    """
    return urlparse(url).netloc

def stream_and_hash(stream: Iterable[bytes]) -> Tuple[bytes, str, int]:
    """Stream content and compute SHA256 hash.
    
    Args:
        stream: Iterator of content chunks
        
    Returns:
        Tuple of (content bytes, SHA256 hash, total size)
    """
    h = hashlib.sha256()
    data = bytearray()
    total = 0
    
    for chunk in stream:
        h.update(chunk)
        data.extend(chunk)
        total += len(chunk)
        
    return bytes(data), h.hexdigest(), total

# Global default metrics instance
METRICS = create_metrics()
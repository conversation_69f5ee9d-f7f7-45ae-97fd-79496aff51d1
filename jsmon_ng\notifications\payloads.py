"""Notification payload models and builders for JSMon-NG.

This module provides:
- Pydantic models for notification payloads
- Payload builders for different notification types
- Common payload formatting utilities
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, HttpUrl

class DiscordEmbed(BaseModel):
    """A Discord embed message."""
    title: str
    description: Optional[str] = None
    color: int
    fields: List[Dict[str, Any]] = Field(default_factory=list)
    footer: Optional[Dict[str, str]] = None
    timestamp: Optional[str] = None

class DiscordPayload(BaseModel):
    """A Discord webhook payload."""
    content: Optional[str] = None
    embeds: List[DiscordEmbed] = Field(default_factory=list)
    username: Optional[str] = None
    avatar_url: Optional[HttpUrl] = None

class TelegramPayload(BaseModel):
    """A Telegram message payload."""
    chat_id: str
    text: str
    parse_mode: str = "HTML"
    disable_web_page_preview: bool = True

class NotificationPayload(BaseModel):
    """Base class for notification payloads."""
    timestamp: datetime = Field(default_factory=lambda: datetime.now())
    type: str
    url: HttpUrl
    message: str
    details: Dict[str, Any] = Field(default_factory=dict)

class FilePayload(BaseModel):
    """A file upload payload."""
    filename: str
    content: bytes
    content_type: str = "application/octet-stream"

def build_discord_embed(
    title: str,
    description: Optional[str] = None,
    color: int = 0x00FF00,
    fields: Optional[List[Dict[str, Any]]] = None,
    footer_text: Optional[str] = None,
    timestamp: Optional[str] = None,
) -> DiscordEmbed:
    """Build a Discord embed with consistent formatting."""
    footer_dict = {"text": footer_text} if footer_text else None
    return DiscordEmbed(
        title=title,
        description=description,
        color=color,
        fields=fields or [],
        footer=footer_dict,
        timestamp=timestamp
    )

def build_discord_payload(
    content: Optional[str] = None,
    embeds: Optional[List[DiscordEmbed]] = None,
    username: Optional[str] = None,
    avatar_url: Optional[str] = None,
) -> DiscordPayload:
    """Build a Discord webhook payload."""
    return DiscordPayload(
        content=content,
        embeds=embeds or [],
        username=username,
        avatar_url=avatar_url,
    )

def build_telegram_payload(
    chat_id: str,
    text: str,
    parse_mode: str = "HTML",
    disable_web_page_preview: bool = True,
) -> TelegramPayload:
    """Build a Telegram message payload."""
    return TelegramPayload(
        chat_id=chat_id,
        text=text,
        parse_mode=parse_mode,
        disable_web_page_preview=disable_web_page_preview,
    )

def build_notification_payload(
    type: str,
    url: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    timestamp: Optional[datetime] = None,
) -> NotificationPayload:
    """Build a generic notification payload."""
    return NotificationPayload(
        type=type,
        url=url,
        message=message,
        details=details or {},
        timestamp=timestamp or datetime.now(),
    )

def build_file_payload(
    filename: str,
    content: bytes,
    content_type: str = "application/octet-stream",
) -> FilePayload:
    """Build a file upload payload."""
    return FilePayload(
        filename=filename,
        content=content,
        content_type=content_type,
    ) 
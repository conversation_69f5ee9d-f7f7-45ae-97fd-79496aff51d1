"""General utilities for JSMon-NG.

This module provides utility functions that are used across the application.
"""

import hashlib
import logging
import re
from typing import Optional, Union
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

def normalize_url(url: str) -> str:
    """Normalize a URL by removing fragments and ensuring consistent format."""
    parsed = urlparse(url)
    # Remove fragment and rebuild URL
    normalized = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
    if parsed.query:
        normalized += f"?{parsed.query}"
    return normalized

def is_valid_url(url: str) -> bool:
    """Check if a URL is valid and uses http/https scheme."""
    try:
        parsed = urlparse(url)
        return parsed.scheme in ('http', 'https') and bool(parsed.netloc)
    except Exception:
        return False

def safe_filename(filename: str, max_length: int = 255) -> str:
    """Convert a string to a safe filename by removing/replacing invalid characters."""
    # Remove or replace invalid characters
    safe = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove control characters
    safe = re.sub(r'[\x00-\x1f\x7f]', '', safe)
    # Trim whitespace and dots from ends
    safe = safe.strip(' .')
    # Ensure it's not empty
    if not safe:
        safe = 'unnamed'
    # Truncate if too long
    if len(safe) > max_length:
        safe = safe[:max_length-3] + '...'
    return safe

def calculate_content_hash(content: Union[str, bytes]) -> str:
    """Calculate SHA256 hash of content."""
    if isinstance(content, str):
        content = content.encode('utf-8')
    return hashlib.sha256(content).hexdigest()

def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """Truncate a string to a maximum length, adding suffix if truncated."""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def format_bytes(size: int) -> str:
    """Format byte size in human readable format."""
    if size < 1024:
        return f"{size} B"
    elif size < 1024 * 1024:
        return f"{size / 1024:.1f} KB"
    elif size < 1024 * 1024 * 1024:
        return f"{size / (1024 * 1024):.1f} MB"
    else:
        return f"{size / (1024 * 1024 * 1024):.1f} GB"

def extract_domain(url: str) -> Optional[str]:
    """Extract domain from URL."""
    try:
        parsed = urlparse(url)
        return parsed.netloc.lower()
    except Exception:
        return None
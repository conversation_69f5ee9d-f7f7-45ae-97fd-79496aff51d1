"""Base notification handler for JSMon-NG.

This module provides:
- Base notification handler with retry and timeout policies
- Common notification handling patterns
- Error handling and logging
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Optional
import aiohttp

from jsmon_ng.state.types import HistoryEntry

logger = logging.getLogger(__name__)

class NotificationHandler(ABC):
    """Base class for notification handlers."""

    def __init__(
        self,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        timeout: float = 30.0,
    ):
        """Initialize the notification handler.

        Args:
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
            timeout: Request timeout in seconds
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        self._session: Optional[aiohttp.ClientSession] = None

    @abstractmethod
    async def send_notification(
        self,
        title: str,
        message: str,
        url: str,
        old_entry: Optional[HistoryEntry] = None,
        new_entry: Optional[HistoryEntry] = None,
        **kwargs: Any,
    ) -> bool:
        """Send a notification.

        Args:
            title: Notification title
            message: Notification message
            url: URL that triggered the notification
            old_entry: Previous history entry
            new_entry: New history entry
            **kwargs: Additional notification-specific arguments

        Returns:
            bool: True if notification was sent successfully
        """
        pass

    async def _retry_with_backoff(
        self,
        func: Any,
        *args: Any,
        **kwargs: Any,
    ) -> Any:
        """Retry a function with exponential backoff.

        Args:
            func: Function to retry
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function

        Returns:
            Any: Result of the function call

        Raises:
            Exception: If all retries fail
        """
        last_exception = None
        for attempt in range(self.max_retries):
            try:
                return await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=self.timeout,
                )
            except asyncio.TimeoutError:
                last_exception = asyncio.TimeoutError(
                    f"Operation timed out after {self.timeout} seconds"
                )
            except Exception as e:
                last_exception = e

            if attempt < self.max_retries - 1:
                delay = self.retry_delay * (2 ** attempt)
                logger.warning(
                    f"Attempt {attempt + 1} failed: {str(last_exception)}. "
                    f"Retrying in {delay:.1f} seconds..."
                )
                await asyncio.sleep(delay)

        raise last_exception

    def _format_timestamp(self, dt: datetime) -> str:
        """Format a timestamp for notifications.

        Args:
            dt: Datetime to format

        Returns:
            str: Formatted timestamp
        """
        return dt.strftime("%Y-%m-%d %H:%M:%S UTC")

    def _format_size(self, size: int) -> str:
        """Format a file size for notifications.

        Args:
            size: Size in bytes

        Returns:
            str: Formatted size
        """
        return f"{size:,} bytes"

    def _format_hash(self, hash_value: str) -> str:
        """Format a hash for notifications.

        Args:
            hash_value: Hash value

        Returns:
            str: Formatted hash
        """
        return f"{hash_value[:8]}..."

    def _format_risk_score(self, score: float) -> str:
        """Format a risk score for notifications.

        Args:
            score: Risk score

        Returns:
            str: Formatted risk score
        """
        return f"{score:.2f}/100"

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create an aiohttp session with proper configuration."""
        if not self._session or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(timeout=timeout)
        return self._session

    async def close(self) -> None:
        """Close the aiohttp session and clean up resources."""
        if self._session and not self._session.closed:
            await self._session.close()
        self._session = None
"""JSMon-NG State Management Module.

This module provides:
- Type definitions and data models for state management
- Enums for finding and error types
- Pydantic models with validation
- State persistence and loading utilities

The module includes:
- Finding and error type enums
- History tracking for URL states
- Statistics collection for monitoring cycles
- Full application state management
"""

from .types import (
    # Enums
    FindingType,
    ErrorType,

    # Core models
    Finding,
    ErrorDetails,
    HistoryEntry,
    History,
    UrlState,
    UrlStats,
    CycleStats,
    SummaryStats,
    FullState,

    # Type aliases
    UrlStateDict,
    UrlStatsDict,
    FindingList,
    ErrorDetailsDict,

    # Helper functions
    ensure_timezone_aware,
)

__all__ = [
    # Enums
    "FindingType",
    "ErrorType",

    # Core models
    "Finding",
    "ErrorDetails",
    "HistoryEntry",
    "History",
    "UrlState",
    "UrlStats",
    "CycleStats",
    "SummaryStats",
    "FullState",

    # Type aliases
    "UrlStateDict",
    "UrlStatsDict",
    "FindingList",
    "ErrorDetailsDict",

    # Helper functions
    "ensure_timezone_aware",
]
# JSMon-NG Configuration File
# This file contains all configuration settings for the JavaScript monitoring application

# --- Target Configuration ---
# Base directory for reconnaissance data
recon_base_directory: "/home/<USER>/my_recon"

# List of domains to monitor for JavaScript files
monitored_targets:
  - "ge.ch"
  - "budgetdirect.com.au"
  - "pokemon.com"
  - "services.klaviyo.com"
  - "staging.sentryx.io"
  - "vanta.com"
  - "geotrust.com"
  - "kpt.ch"
  - "szkb.ch"
  - "valiant.ch"
  - "thawte.com"
  - "carvana.com"
  - "jungfrau.ch"
  - "baloise.com"
  - "lienhardt.ch"
  - "bexio.com"
  - "sak.ch"
  - "kkg.ch"
  - "adcubum.com"
  - "junelife.com"
  - "walker-cloud.com"
  - "aboutyou.de"
  - "sulzer.com"
  - "lusha.com"
  - "lookfantastic.com"
  - "myprotein.com"
  - "ingenuitycloudservices.com"

# --- Core Application Settings ---
download_directory: "./downloads"
state_file: "./jsmon_state.json"
run_interval_seconds: 12000 # 3.33 hours
target_files: [] # Additional specific files to monitor

# --- Performance and Concurrency Settings ---
concurrency:
  max_fetch_workers: 10
  max_concurrent_per_domain: 3 # Reduced to be more respectful to target servers

# --- Network Configuration ---
network:
  max_retries: 3 # Increased for better reliability
  retry_delay: 2.0
  verify_ssl: true # Security best practice - always verify SSL
  timeout: 60.0
  connect_timeout: 10.0
  read_timeout: 50.0
  max_redirects: 5

  # Standard browser headers to avoid detection
  headers:
    User-Agent: "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0"
    Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
    Accept-Language: "en-US,en;q=0.5"
    Accept-Encoding: "gzip, deflate, br"
    Connection: "keep-alive"
    Upgrade-Insecure-Requests: "1"
    Sec-Fetch-Dest: "document"
    Sec-Fetch-Mode: "navigate"
    Sec-Fetch-Site: "none"
    Sec-Fetch-User: "?1"
    Cache-Control: "max-age=0"

  # Circuit breaker for handling failures gracefully
  circuit_breaker:
    failure_threshold: 5 # Increased threshold
    recovery_timeout: 300 # 5 minutes
    half_open_timeout: 30

# --- Risk Assessment and Severity Configuration ---
severity:
  # Weights for different finding types (0-100)
  type_weights:
    secret: 100 # Highest priority - exposed secrets
    vulnerability: 90 # Security vulnerabilities
    pattern: 50 # Suspicious patterns
    endpoint: 30 # API endpoints
    link: 10 # External links

  # Specific pattern weights for fine-tuning
  pattern_weights:
    secret: 95 # Generic secrets
    password: 90 # Password-related
    token: 85 # Authentication tokens
    credential: 85 # Credentials
    api_key: 80 # API keys

# --- HTTP Client Configuration ---
fetcher:
  # Connection settings
  max_connections: 50
  max_keepalive: 10 # Increased for better performance
  http2_enabled: true
  trust_env: false # Don't trust environment proxy settings

  # Caching and normalization
  cache_normalize_scheme: true
  cache_normalize_query: true
  strip_fragments_for_cache_key: true
  strip_query_params: false
  cache_remove_params: [] # Parameters to remove for caching

# --- Content Analysis and Scanning Configuration ---
scanner:
  # Basic pattern scanning (lightweight)
  basic_pattern_scanning: false

  # Link and URL extraction
  link_extraction:
    enabled: true
    notify_new_links: true
    track_changes: true
    categorize_links: true

    # Patterns to extract different types of URLs
    patterns:
      - "https?://[^\\s\"'<>]+" # Standard HTTP/HTTPS URLs
      - "//[^\\s\"'<>]+" # Protocol-relative URLs
      - "data:[^\\s\"'<>]+" # Data URLs
      - "blob:[^\\s\"'<>]+" # Blob URLs
      - "javascript:[^\\s\"'<>]+" # JavaScript URLs
      - "about:[^\\s\"'<>]+" # About URLs
      - "file:[^\\s\"'<>]+" # File URLs
      - "chrome:[^\\s\"'<>]+" # Chrome extension URLs

    # Patterns to exclude from extraction
    exclude_patterns:
      - "data:image/[^\\s\"'<>]+" # Exclude image data URLs
      - "data:font/[^\\s\"'<>]+" # Exclude font data URLs

  # Vulnerability detection (experimental)
  vulnerability_detection:
    enabled: false # Disabled by default due to performance impact
    min_severity: "medium"
    max_findings_per_file: 50 # Reduced to prevent spam

    detectors:
      - "prototype_pollution"
      - "template_injection"
      - "dom_xss"
      - "insecure_config"

  # TruffleHog integration for secret detection
  trufflehog:
    enabled: true
    command: "/usr/local/bin/trufflehog"
    timeout: 120 # Increased timeout for large files
    max_concurrent_scans: 5 # Reduced to prevent resource exhaustion
    extra_args:
      - "--no-update"
      - "--json"
      - "--results=verified"
      - "--fail" # Exit with non-zero on findings

# --- Application State Management ---
state:
  # Memory management
  max_memory_mb: 512.0 # Increased slightly for better performance
  memory_limit_action: "warn_and_continue"

  # File handling and backup
  backup_corrupt_files: true
  atomic_state_updates: true
  incremental_state_backup: true
  backup_interval_hours: 6
  recovery_mode: "auto"

  # Locking and concurrency
  lock_timeout: 15 # Increased for better reliability
  summary_lock_timeout: 10 # Increased for consistency

# --- Data Cleanup Configuration ---
cleanup:
  enabled: true
  keep_versions: 5 # Increased for better history tracking
  cleanup_interval_hours: 24 # Daily cleanup
  max_file_age_days: 30 # Remove files older than 30 days

# --- Error Handling and Recovery ---
error_handling:
  # URL management
  prune_after_consecutive_404: 10 # Increased tolerance
  max_error_rate: 0.5 # Maximum error rate before disabling features

  # Feature management under stress
  disable_features_on_timeout:
    - "trufflehog"
    - "vulnerability_detection"
    - "source_maps"

  # Health monitoring
  health_check_interval: 1800 # 30 minutes - more frequent checks

# --- Logging Configuration ---
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
  file: "jsmon.log" # Consistent naming
  max_size: 10485760 # 10MB
  backup_count: 5
  date_format: "%Y-%m-%d %H:%M:%S"

  # Handler-specific configuration
  handlers:
    console:
      level: "INFO"
      format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s" # Simplified for console
    file:
      level: "DEBUG" # More detailed logging to file
      format: "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s"
      filename: "jsmon.log"

  # Logger-specific levels (reduced noise from external libraries)
  loggers:
    httpcore:
      level: "WARNING" # Reduced from INFO
      propagate: true
    httpx:
      level: "WARNING" # Reduced from INFO
      propagate: true
    jsmon_ng.network:
      level: "INFO"
      propagate: true
    asyncio:
      level: "WARNING" # Reduced from INFO
      propagate: true
    hpack:
      level: "WARNING" # Reduced from INFO
      propagate: true
    urllib3:
      level: "WARNING" # Added common noisy logger
      propagate: true

# --- Notification Configuration ---
notifications:
  # Notification triggers
  notify_on_fetch_error: false
  notify_on_processing_error: false
  notify_on_new_content: true
  notify_on_changed_content: true
  notify_on_new_findings: true

  # Discord integration
  discord:
    enabled: false # Disabled by default - set webhook_url to enable
    webhook_url: null # Set to your Discord webhook URL
    username: "jsmon"
    avatar_url: null
    color: 0x3498DB # Blue
    include_preview: true
    max_preview_length: 1000

  # Telegram integration (disabled by default)
  telegram:
    enabled: false
    bot_token: null # Set to your Telegram bot token
    chat_id: null # Set to your Telegram chat ID
    timeout: 30 # Increased timeout

  # Retry configuration for failed notifications
  retry:
    max_retries: 3
    retry_delay: 2.0 # Increased delay
    timeout: 30.0

  # Color scheme for different notification types
  colors:
    change: 0xFFA500 # Orange
    secret: 0xFF0000 # Red
    pattern: 0xFFFF00 # Yellow (changed from cyan for better visibility)
    endpoint: 0x778899 # Blue-gray
    vulnerability: 0x8B0000 # Dark red
    sourcemap: 0x8A2BE2 # Blue-violet
    error: 0xDC143C # Crimson
    summary: 0x32CD32 # Lime green (changed for better contrast)

  # Enhanced formatting options
  enhanced_formatting:
    enabled: true
    use_emoji: true
    use_markdown: true
    include_context: true
    max_context_length: 300 # Increased for more context
    color_coded_severity: true
    truncate_long_messages: true
    max_message_length: 2000 # Discord limit consideration

"""Template injection detector for JavaScript files.

This module provides functionality to detect potential template injection vulnerabilities.
"""

import re
import logging
from typing import Dict, List, Any

# --- Project Modules ---
from jsmon_ng.config.config import AppConfig

log = logging.getLogger(__name__)

# Default configuration values
DEFAULT_CONTEXT_SIZE = 200  # Characters to include before and after matches for analysis
DEFAULT_DISPLAY_CONTEXT_SIZE = 100  # Characters to include before and after matches for display

class TemplateInjectionDetector:
    """Detector for client-side template injection vulnerabilities."""
    
    def __init__(self, config: AppConfig):
        """Initialize the detector with configuration."""
        self.config = config
        self.enabled = config.scanner.vulnerability_detection.enabled and 'template_injection' in config.scanner.vulnerability_detection.detectors
        
        # Patterns for different template engines
        self.template_patterns = {
            'angular': [
                re.compile(r'\{\{.*?\}\}', re.DOTALL),  # Angular interpolation
                re.compile(r'\[\(.*?\)\]', re.DOTALL),  # Angular two-way binding
                re.compile(r'(?:ng-|v-|data-ng-|x-ng-)(?:bind|model|if|show|hide|repeat|switch|include|src|href)', re.IGNORECASE)  # Angular directives
            ],
            'vue': [
                re.compile(r'\{\{.*?\}\}', re.DOTALL),  # Vue interpolation
                re.compile(r'(?:v-|:)(?:bind|model|if|show|html|text|on|for|once)', re.IGNORECASE)  # Vue directives
            ],
            'handlebars': [
                re.compile(r'\{\{.*?\}\}', re.DOTALL),  # Handlebars/Mustache interpolation
                re.compile(r'\{\{#.*?\}\}.*?\{\{/.*?\}\}', re.DOTALL),  # Handlebars blocks
                re.compile(r'\{\{\^.*?\}\}.*?\{\{/.*?\}\}', re.DOTALL)  # Handlebars inverse blocks
            ],
            'react': [
                re.compile(r'<\w+.*?\{.*?\}.*?>', re.DOTALL),  # JSX expressions
                re.compile(r'dangerouslySetInnerHTML=\{\{__html:.*?\}\}', re.DOTALL)  # React dangerous HTML
            ],
            'ember': [
                re.compile(r'\{\{.*?\}\}', re.DOTALL),  # Ember interpolation
                re.compile(r'\{\{#.*?\}\}.*?\{\{/.*?\}\}', re.DOTALL),  # Ember blocks
                re.compile(r'\{\{\^.*?\}\}.*?\{\{/.*?\}\}', re.DOTALL)  # Ember inverse blocks
            ]
        }
        
        # Dangerous patterns that might indicate template injection
        self.dangerous_patterns = [
            re.compile(r'(?:eval|Function|setTimeout|setInterval)\s*\(\s*(?:["\'`].*?\$\{.*?["\'`]|.*?\$\{.*?)', re.DOTALL | re.IGNORECASE),
            re.compile(r'(?:innerHTML|outerHTML)\s*=\s*(?:["\'`].*?\$\{.*?["\'`]|.*?\$\{.*?)', re.DOTALL | re.IGNORECASE),
            re.compile(r'document\.write\s*\(\s*(?:["\'`].*?\$\{.*?["\'`]|.*?\$\{.*?)', re.DOTALL | re.IGNORECASE),
            re.compile(r'\.html\s*\(\s*(?:["\'`].*?\$\{.*?["\'`]|.*?\$\{.*?)', re.DOTALL | re.IGNORECASE)
        ]
        
        # User input sources
        self.input_sources = [
            re.compile(r'location\.(?:href|search|hash|pathname)', re.IGNORECASE),
            re.compile(r'document\.(?:URL|documentURI|referrer|cookie)', re.IGNORECASE),
            re.compile(r'(?:localStorage|sessionStorage)\.getItem', re.IGNORECASE),
            re.compile(r'(?:window\.name|history\.state)', re.IGNORECASE),
            re.compile(r'(?:event|e)\.target\.value', re.IGNORECASE),
            re.compile(r'URLSearchParams', re.IGNORECASE),
            re.compile(r'FormData', re.IGNORECASE)
        ]
        
        log.info(f"Initialized TemplateInjectionDetector with {sum(len(p) for p in self.template_patterns.values())} template patterns")
    
    def scan_content(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Scan content for client-side template injection vulnerabilities.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of findings
        """
        if not self.enabled:
            return []
        
        findings = []
        
        # Detect template engine usage
        template_engine_findings = self._detect_template_engines(content, url)
        findings.extend(template_engine_findings)
        
        # Detect dangerous template patterns
        dangerous_findings = self._detect_dangerous_patterns(content, url)
        findings.extend(dangerous_findings)
        
        if findings:
            log.info(f"Found {len(findings)} potential template injection vulnerabilities in {url}")
        
        return findings
    
    def _detect_template_engines(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Detect template engine usage and potential injection points.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of findings
        """
        findings = []
        
        for engine, patterns in self.template_patterns.items():
            for pattern in patterns:
                for match in pattern.finditer(content):
                    template_content = match.group(0)
                    
                    # Check if the template contains dynamic content from user input
                    has_user_input = False
                    for source in self.input_sources:
                        if source.search(content[max(0, match.start() - 200):min(len(content), match.end() + 200)]):
                            has_user_input = True
                            break
                    
                    # Only report if there's potential user input
                    if has_user_input:
                        line_number = content.count('\n', 0, match.start()) + 1
                        context_start = max(0, match.start() - 100)
                        context_end = min(len(content), match.end() + 100)
                        context = content[context_start:context_end]
                        
                        findings.append({
                            'type': 'vulnerability',
                            'vulnerability_type': 'template_injection',
                            'template_engine': engine,
                            'match': template_content[:100] + ('...' if len(template_content) > 100 else ''),
                            'line': line_number,
                            'url': url,
                            'context': context,
                            'severity': 'medium',
                            'confidence': 0.7,
                            'description': f"Potential {engine} template injection vulnerability"
                        })
        
        return findings
    
    def _detect_dangerous_patterns(self, content: str, url: str) -> List[Dict[str, Any]]:
        """
        Detect dangerous patterns that might indicate template injection.
        
        Args:
            content: The content to scan
            url: The URL of the content
            
        Returns:
            List of findings
        """
        findings = []
        
        for pattern in self.dangerous_patterns:
            for match in pattern.finditer(content):
                line_number = content.count('\n', 0, match.start()) + 1
                context_start = max(0, match.start() - 100)
                context_end = min(len(content), match.end() + 100)
                context = content[context_start:context_end]
                
                # Check if there's potential user input
                has_user_input = False
                for source in self.input_sources:
                    if source.search(content[max(0, match.start() - 200):min(len(content), match.end() + 200)]):
                        has_user_input = True
                        break
                
                severity = "high" if has_user_input else "medium"
                
                findings.append({
                    'type': 'vulnerability',
                    'vulnerability_type': 'template_injection',
                    'template_engine': 'unknown',
                    'match': match.group(0)[:100] + ('...' if len(match.group(0)) > 100 else ''),
                    'line': line_number,
                    'url': url,
                    'context': context,
                    'severity': severity,
                    'confidence': 0.8 if has_user_input else 0.6,
                    'description': "Dangerous template usage pattern" + (" with potential user input" if has_user_input else "")
                })
        
        return findings
